"""Processors for the model training step of the worklow."""
import logging
import os.path as op
import mlflow

from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression

from ta_lib.core.api import (
    get_dataframe,
    get_feature_names_from_column_transformer,
    load_dataset,
    load_pipeline,
    register_processor,
    save_pipeline,
    DEFAULT_ARTIFACTS_PATH
)
from ta_lib.regression.api import SKLStatsmodelOLS
from production.mlflow_integration import (
    init_mlflow,
    log_model_metrics,
    log_model_params,
    log_sklearn_model,
    log_dataset_info,
    log_data_quality_metrics,
    start_mlflow_run_with_tags,
    log_processing_time,
    log_model_artifact
)
from production.mlflow_config import get_experiment_name

logger = logging.getLogger(__name__)


@register_processor("model-gen", "train-model")
def train_model(context, params):
    """Train a regression model."""
    from datetime import datetime

    artifacts_folder = DEFAULT_ARTIFACTS_PATH

    input_features_ds = "train/housing/features"
    input_target_ds = "train/housing/target"

    # Initialize MLflow
    experiment_name = get_experiment_name("model-gen")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="train_model",
        job_name="model-gen",
        stage_name="train-model",
        model_type="regression"
    ):
        start_time = datetime.now()

        # Determine model type from parameters
        model_type = params.get("model_type", "regression")
        algorithm = params.get("algorithm", "random_forest")  # Default to Random Forest for housing

        # Log parameters
        log_model_params(params)
        mlflow.log_param("input_features_dataset", input_features_ds)
        mlflow.log_param("input_target_dataset", input_target_ds)
        mlflow.log_param("model_type", model_type)
        mlflow.log_param("model_algorithm", algorithm)
        mlflow.log_param("random_seed", context.random_seed)

        # load training datasets
        train_X = load_dataset(context, input_features_ds)
        train_y = load_dataset(context, input_target_ds)

        # Log input dataset info
        log_dataset_info(train_X, "train_features")
        log_dataset_info(train_y, "train_target")
        log_data_quality_metrics(train_X, "train_features")

        # load pre-trained feature pipelines and other artifacts
        curated_columns_path = op.join(artifacts_folder, "curated_columns.joblib")
        features_transformer_path = op.join(artifacts_folder, "features.joblib")

        curated_columns = load_pipeline(curated_columns_path)
        features_transformer = load_pipeline(features_transformer_path)

        # Log pipeline info
        mlflow.log_param("curated_columns_count", len(curated_columns))
        mlflow.log_param("feature_transformer_loaded", True)

        # transform the training dataset
        train_X_transformed = get_dataframe(
            features_transformer.transform(train_X),
            get_feature_names_from_column_transformer(features_transformer),
        )
        train_X_final = train_X_transformed[curated_columns]

        # Log transformation metrics
        transformation_metrics = {
            "features_before_transformation": len(train_X.columns),
            "features_after_transformation": len(train_X_transformed.columns),
            "features_after_curation": len(train_X_final.columns),
            "training_samples": len(train_X_final)
        }
        mlflow.log_metrics(transformation_metrics)

        # Log final dataset info
        log_dataset_info(train_X_final, "final_train_features")
        log_data_quality_metrics(train_X_final, "final_train_features")

        # Create model based on algorithm parameter
        if algorithm == "random_forest":
            # Random Forest with p parameters support
            p_params = params.get("p", {})

            # Default parameters
            rf_params = {
                "n_estimators": 100,
                "max_depth": None,
                "min_samples_split": 2,
                "min_samples_leaf": 1,
                "max_features": "auto",
                "bootstrap": True,
                "oob_score": False,
                "random_state": context.random_seed,
                "n_jobs": -1
            }

            # Override with p parameters
            rf_params.update(p_params)

            # Ensure random_state is set correctly
            rf_params["random_state"] = context.random_seed

            estimator = RandomForestRegressor(**rf_params)

            # Log all parameters
            for param_name, param_value in rf_params.items():
                mlflow.log_param(f"rf_{param_name}", param_value)

        elif algorithm == "linear_regression":
            # Linear Regression with p parameters support
            p_params = params.get("p", {})
            lr_params = {
                "fit_intercept": True,
                "normalize": False,
                "copy_X": True,
                "n_jobs": None
            }
            lr_params.update(p_params)

            estimator = LinearRegression(**lr_params)

            # Log parameters
            for param_name, param_value in lr_params.items():
                mlflow.log_param(f"lr_{param_name}", param_value)

        elif algorithm == "ols":
            # OLS with p parameters support
            p_params = params.get("p", {})
            estimator = SKLStatsmodelOLS()

            # Log p parameters for OLS
            for param_name, param_value in p_params.items():
                mlflow.log_param(f"ols_{param_name}", param_value)

        else:
            # Default to Random Forest with p parameters
            p_params = params.get("p", {})
            rf_params = {
                "n_estimators": 100,
                "random_state": context.random_seed,
                "n_jobs": -1
            }
            rf_params.update(p_params)
            rf_params["random_state"] = context.random_seed

            estimator = RandomForestRegressor(**rf_params)

            # Log parameters
            for param_name, param_value in rf_params.items():
                mlflow.log_param(f"default_rf_{param_name}", param_value)

        # create training pipeline
        reg_pipeline = Pipeline([("estimator", estimator)])

        # fit the training pipeline
        reg_pipeline.fit(train_X_final, train_y.values.ravel())

        # Calculate and log metrics
        train_predictions = reg_pipeline.predict(train_X_final)
        mse = mean_squared_error(train_y, train_predictions)
        r2 = r2_score(train_y, train_predictions)
        rmse = mse ** 0.5

        # Additional metrics
        mae = mean_absolute_error(train_y, train_predictions)

        metrics = {
            "training_mse": mse,
            "training_rmse": rmse,
            "training_mae": mae,
            "training_r2": r2
        }
        log_model_metrics(metrics)

        # Log model to MLflow
        log_sklearn_model(reg_pipeline, "housing_regression_model")

        # save fitted training pipeline
        model_path = op.abspath(op.join(artifacts_folder, "train_pipeline.joblib"))
        save_pipeline(reg_pipeline, model_path)

        # Log the saved model as an artifact
        log_model_artifact(model_path, "model_artifacts")

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "model_training")

        return {"model_path": model_path, "metrics": metrics}
