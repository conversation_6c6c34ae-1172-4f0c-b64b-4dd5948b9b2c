"""
Custom transformers for housing price prediction.

This module contains custom transformers that support both forward and inverse
transforms, specifically designed for housing data preprocessing.
"""

import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.utils.validation import check_array, check_is_fitted
from sklearn.preprocessing import StandardScaler
from typing import Optional, Union, Dict, Any
import warnings


class HousingFeatureTransformer(BaseEstimator, TransformerMixin):
    """
    Custom transformer for housing features with forward and inverse transforms.
    
    This transformer creates derived features from housing data and supports
    both forward transformation (for training/prediction) and inverse 
    transformation (for interpretability).
    
    Features created:
    - rooms_per_household: total_rooms / households
    - bedrooms_per_room: total_bedrooms / total_rooms  
    - population_per_household: population / households
    - income_scaled: standardized median_income
    
    Parameters
    ----------
    scale_income : bool, default=True
        Whether to standardize the median_income feature
    handle_inf : bool, default=True
        Whether to handle infinite values by replacing with median
    
    Attributes
    ----------
    income_scaler_ : StandardScaler
        Fitted scaler for median_income (if scale_income=True)
    feature_medians_ : dict
        Median values for handling infinite values
    n_features_in_ : int
        Number of input features
    feature_names_in_ : array-like
        Names of input features
    """
    
    def __init__(self, scale_income: bool = True, handle_inf: bool = True):
        self.scale_income = scale_income
        self.handle_inf = handle_inf
    
    def fit(self, X: Union[np.ndarray, pd.DataFrame], y: Optional[np.ndarray] = None):
        """
        Fit the transformer to the data.
        
        Parameters
        ----------
        X : array-like of shape (n_samples, n_features)
            Input data
        y : array-like, optional
            Target values (ignored)
            
        Returns
        -------
        self : object
            Returns self
        """
        # Convert to DataFrame if numpy array
        if isinstance(X, np.ndarray):
            X = pd.DataFrame(X)
        
        # Store input information
        self.n_features_in_ = X.shape[1]
        if hasattr(X, 'columns'):
            self.feature_names_in_ = X.columns.tolist()
        
        # Create derived features to get medians for inf handling
        X_transformed = self._create_features(X)
        
        # Store medians for handling infinite values
        if self.handle_inf:
            self.feature_medians_ = {}
            derived_cols = ['rooms_per_household', 'bedrooms_per_room', 'population_per_household']
            for col in derived_cols:
                if col in X_transformed.columns:
                    finite_values = X_transformed[col][np.isfinite(X_transformed[col])]
                    if len(finite_values) > 0:
                        self.feature_medians_[col] = finite_values.median()
                    else:
                        self.feature_medians_[col] = 0.0
        
        # Fit income scaler if requested
        if self.scale_income and 'median_income' in X.columns:
            self.income_scaler_ = StandardScaler()
            self.income_scaler_.fit(X[['median_income']])
        
        return self
    
    def transform(self, X: Union[np.ndarray, pd.DataFrame]) -> pd.DataFrame:
        """
        Transform the input data.
        
        Parameters
        ----------
        X : array-like of shape (n_samples, n_features)
            Input data
            
        Returns
        -------
        X_transformed : DataFrame of shape (n_samples, n_features_out)
            Transformed data with derived features
        """
        check_is_fitted(self)
        
        # Convert to DataFrame if numpy array
        if isinstance(X, np.ndarray):
            if hasattr(self, 'feature_names_in_'):
                X = pd.DataFrame(X, columns=self.feature_names_in_)
            else:
                X = pd.DataFrame(X)
        
        # Create derived features
        X_transformed = self._create_features(X.copy())
        
        # Handle infinite values
        if self.handle_inf and hasattr(self, 'feature_medians_'):
            for col, median_val in self.feature_medians_.items():
                if col in X_transformed.columns:
                    X_transformed[col] = X_transformed[col].replace([np.inf, -np.inf], median_val)
        
        # Scale income if requested
        if self.scale_income and hasattr(self, 'income_scaler_') and 'median_income' in X_transformed.columns:
            X_transformed['income_scaled'] = self.income_scaler_.transform(X_transformed[['median_income']])
        
        return X_transformed
    
    def inverse_transform(self, X: Union[np.ndarray, pd.DataFrame]) -> pd.DataFrame:
        """
        Inverse transform the data back to original feature space.
        
        Parameters
        ----------
        X : array-like of shape (n_samples, n_features_out)
            Transformed data
            
        Returns
        -------
        X_original : DataFrame of shape (n_samples, n_features_in)
            Data in original feature space
        """
        check_is_fitted(self)
        
        # Convert to DataFrame if numpy array
        if isinstance(X, np.ndarray):
            X = pd.DataFrame(X)
        
        X_inverse = X.copy()
        
        # Inverse transform income scaling
        if self.scale_income and hasattr(self, 'income_scaler_') and 'income_scaled' in X_inverse.columns:
            if 'median_income' not in X_inverse.columns:
                # Reconstruct median_income from scaled version
                X_inverse['median_income'] = self.income_scaler_.inverse_transform(
                    X_inverse[['income_scaled']]
                ).flatten()
            # Remove scaled version
            X_inverse = X_inverse.drop(columns=['income_scaled'], errors='ignore')
        
        # Remove derived features (keep only original features)
        derived_features = ['rooms_per_household', 'bedrooms_per_room', 'population_per_household']
        X_inverse = X_inverse.drop(columns=derived_features, errors='ignore')
        
        # Ensure we have the original features
        if hasattr(self, 'feature_names_in_'):
            missing_features = set(self.feature_names_in_) - set(X_inverse.columns)
            if missing_features:
                warnings.warn(f"Cannot fully inverse transform. Missing original features: {missing_features}")
        
        return X_inverse
    
    def _create_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Create derived features from housing data."""
        X_new = X.copy()
        
        # Rooms per household
        if 'total_rooms' in X_new.columns and 'households' in X_new.columns:
            X_new['rooms_per_household'] = X_new['total_rooms'] / X_new['households']
        
        # Bedrooms per room ratio
        if 'total_bedrooms' in X_new.columns and 'total_rooms' in X_new.columns:
            X_new['bedrooms_per_room'] = X_new['total_bedrooms'] / X_new['total_rooms']
        
        # Population per household
        if 'population' in X_new.columns and 'households' in X_new.columns:
            X_new['population_per_household'] = X_new['population'] / X_new['households']
        
        return X_new
    
    def get_feature_names_out(self, input_features=None):
        """Get output feature names for transformation."""
        check_is_fitted(self)
        
        if input_features is None:
            if hasattr(self, 'feature_names_in_'):
                input_features = self.feature_names_in_
            else:
                input_features = [f'x{i}' for i in range(self.n_features_in_)]
        
        # Start with input features
        output_features = list(input_features)
        
        # Add derived features
        derived_features = ['rooms_per_household', 'bedrooms_per_room', 'population_per_household']
        output_features.extend(derived_features)
        
        # Add scaled income if applicable
        if self.scale_income and 'median_income' in input_features:
            output_features.append('income_scaled')
        
        return np.array(output_features)


class LogTransformer(BaseEstimator, TransformerMixin):
    """
    Log transformer with inverse transform capability.
    
    Applies log(1 + x) transformation to handle zero values and provides
    inverse transformation.
    
    Parameters
    ----------
    columns : list, optional
        Specific columns to transform. If None, transforms all numeric columns.
    """
    
    def __init__(self, columns: Optional[list] = None):
        self.columns = columns
    
    def fit(self, X, y=None):
        """Fit the transformer."""
        if isinstance(X, pd.DataFrame):
            if self.columns is None:
                self.columns_ = X.select_dtypes(include=[np.number]).columns.tolist()
            else:
                self.columns_ = self.columns
        else:
            self.columns_ = self.columns or list(range(X.shape[1]))
        
        return self
    
    def transform(self, X):
        """Apply log(1 + x) transformation."""
        check_is_fitted(self)
        
        if isinstance(X, pd.DataFrame):
            X_transformed = X.copy()
            for col in self.columns_:
                if col in X_transformed.columns:
                    X_transformed[col] = np.log1p(X_transformed[col])
        else:
            X_transformed = X.copy()
            for col_idx in self.columns_:
                X_transformed[:, col_idx] = np.log1p(X_transformed[:, col_idx])
        
        return X_transformed
    
    def inverse_transform(self, X):
        """Apply inverse transformation: exp(x) - 1."""
        check_is_fitted(self)
        
        if isinstance(X, pd.DataFrame):
            X_inverse = X.copy()
            for col in self.columns_:
                if col in X_inverse.columns:
                    X_inverse[col] = np.expm1(X_inverse[col])
        else:
            X_inverse = X.copy()
            for col_idx in self.columns_:
                X_inverse[:, col_idx] = np.expm1(X_inverse[:, col_idx])
        
        return X_inverse
