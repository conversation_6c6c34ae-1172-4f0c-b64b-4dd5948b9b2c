import os
import os.path as op
import subprocess
import sys
from functools import partial

import click

from ta_lib.core.api import (
    create_context,
    job_planner,
    job_runner,
    list_jobs,
    load_job_processors
)
from production.mlflow_config import get_mlflow_ui_url, get_artifact_location

HERE = op.dirname(op.abspath(__file__))


@click.group()
@click.option("-c", "--cfg", required=False)
@click.pass_context
def cli(cli_ctx, cfg):
    """Invoke various ML workflows."""
    if cfg is None:
        cfg = os.environ.get("TA_LIB_APP_CONFIG_PATH")
        # by default look for a config.yml in conf folder relative to the script
        if cfg is None:
            cfg = os.path.join(HERE, "conf", "config.yml")

    cli_ctx.ensure_object(dict)
    proj_ctxt = create_context(cfg)
    cli_ctx.obj["project_context"] = proj_ctxt


# --------------
# command groups
# --------------
@cli.group()
@click.pass_context
def job(cli_ctx):
    """Command group for Job management tasks."""
    pass


@cli.group()
@click.pass_context
def mlflow(cli_ctx):
    """Command group for MLflow management tasks."""
    pass


# -------------
# Job commands
# -------------
@job.command("list")
@click.pass_context
def _list_jobs(cli_ctx):

    # -------------
    # Data commands
    # -------------
    # FIXME: move this into a function/context manager
    # use tabulate ?
    header = "Available jobs"
    print("-" * len(header))
    print(header)
    print("-" * len(header))
    for key in list_jobs():
        print(key)


@job.command("run")
@click.option(
    "-j", "--job-id", default="all", help="Id of the job to run",
)
@click.option(
    "-n", "--num-workers", default=1, help="Number of worker processes",
)
@click.option(
    "-t",
    "--num-threads-per-worker",
    default=-1,
    help="Number of threads per each worker process",
)
@click.pass_context
def _run_job(cli_ctx, job_id, num_workers, num_threads_per_worker):

    proj_ctxt = cli_ctx.obj["project_context"]
    job_catalog = proj_ctxt.job_catalog

    init_fn = None
    if num_workers != 1:
        init_fn = partial(load_job_processors, op.dirname(op.abspath(__file__)))

    _completed = False
    for job_spec in job_catalog["jobs"]:
        spec_job_id = job_spec["name"]
        if (job_id != "all") and (spec_job_id != job_id):
            continue

        # FIXME: if needed, add decorators for job_planners and task_runners
        # and associate with job_id
        planner = job_planner.create_job_plan
        job_runner.main(
            proj_ctxt,
            planner,
            job_spec,
            init_fn=init_fn,
            n_workers=num_workers,
            n_threads_per_worker=num_threads_per_worker,
        )
        _completed = True

    if not _completed:
        print(
            f"Invalid job-id : {job_id}. \n\n"
            "Use list sub-command to see available tasks."
        )
        if not _completed:
            print(
                f"Invalid job-id : {job_id}. \n\n"
                "Use list sub-command to see available tasks."
            )


# ---------------
# MLflow commands
# ---------------
@mlflow.command("ui")
@click.option(
    "--port", default=5000, help="Port to run MLflow UI on"
)
@click.option(
    "--host", default="localhost", help="Host to run MLflow UI on"
)
@click.pass_context
def _start_mlflow_ui(cli_ctx, port, host):
    """Start the MLflow UI server."""
    import subprocess
    import sys

    artifact_location = get_artifact_location()

    print(f"Starting MLflow UI...")
    print(f"Artifact location: {artifact_location}")
    print(f"UI will be available at: http://{host}:{port}")
    print("Press Ctrl+C to stop the server")

    try:
        # Start MLflow UI
        cmd = [
            sys.executable, "-m", "mlflow", "ui",
            "--backend-store-uri", f"file://{artifact_location}",
            "--host", host,
            "--port", str(port)
        ]

        subprocess.run(cmd, check=True)

    except KeyboardInterrupt:
        print("\nMLflow UI server stopped.")
    except subprocess.CalledProcessError as e:
        print(f"Error starting MLflow UI: {e}")
        sys.exit(1)


@mlflow.command("experiments")
@click.pass_context
def _list_experiments(cli_ctx):
    """List all MLflow experiments."""
    import mlflow
    from production.mlflow_integration import init_mlflow

    # Initialize MLflow
    init_mlflow()

    client = mlflow.tracking.MlflowClient()
    experiments = client.search_experiments()

    if not experiments:
        print("No experiments found.")
        return

    print("MLflow Experiments:")
    print("-" * 50)
    for exp in experiments:
        print(f"ID: {exp.experiment_id}")
        print(f"Name: {exp.name}")
        print(f"Lifecycle Stage: {exp.lifecycle_stage}")
        if exp.tags:
            print(f"Tags: {exp.tags}")
        print("-" * 50)


@mlflow.command("runs")
@click.option(
    "--experiment-name", help="Filter runs by experiment name"
)
@click.option(
    "--limit", default=10, help="Limit number of runs to display"
)
@click.pass_context
def _list_runs(cli_ctx, experiment_name, limit):
    """List recent MLflow runs."""
    import mlflow
    from production.mlflow_integration import init_mlflow

    # Initialize MLflow
    init_mlflow()

    client = mlflow.tracking.MlflowClient()

    if experiment_name:
        experiment = mlflow.get_experiment_by_name(experiment_name)
        if not experiment:
            print(f"Experiment '{experiment_name}' not found.")
            return
        experiment_ids = [experiment.experiment_id]
    else:
        experiments = client.search_experiments()
        experiment_ids = [exp.experiment_id for exp in experiments]

    runs = client.search_runs(
        experiment_ids=experiment_ids,
        max_results=limit,
        order_by=["start_time DESC"]
    )

    if not runs:
        print("No runs found.")
        return

    print(f"Recent MLflow Runs (showing {len(runs)} runs):")
    print("-" * 80)
    for run in runs:
        print(f"Run ID: {run.info.run_id}")
        print(f"Experiment ID: {run.info.experiment_id}")
        print(f"Status: {run.info.status}")
        print(f"Start Time: {run.info.start_time}")
        if run.info.run_name:
            print(f"Run Name: {run.info.run_name}")
        if run.data.tags:
            print(f"Tags: {dict(run.data.tags)}")
        if run.data.metrics:
            print(f"Metrics: {dict(run.data.metrics)}")
        print("-" * 80)


# ------------------
# Initialize the CLI
# ------------------
def main():
    # load the processing jobs defined in the current folder
    load_job_processors(op.dirname(op.abspath(__file__)))
    cli()


if __name__ == "__main__":
    main()
