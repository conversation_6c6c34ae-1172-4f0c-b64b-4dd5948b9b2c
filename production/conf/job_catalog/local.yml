jobs:
  - name: data-cleaning
    stages:
      - name: "clean-base-tables"
        tasks:
          - name: "housing"
            params: {}

      - name: "train-test-split"
        tasks:
          - name: "train-test"
            params:
              target: median_house_value
              test_size: 0.2

  - name: feat-engg
    stages:
      - name: "feature-pipelines"
        tasks:
          - name: "transform-features"
            params:
              outliers:
                method: mean
                drop: False
              sampling_fraction: 0.1
              # Custom HousingFeatureTransformer parameters
              scale_income: true
              handle_inf: true

  - name: model-gen
    stages:
      - name: "model-creation"
        tasks:
          - name: "train-model"
            params:
              sampling_fraction: 0.1
              model_type: regression
              algorithm: random_forest
              # P parameters for flexible model configuration
              p:
                n_estimators: 100
                max_depth: null
                min_samples_split: 2
                min_samples_leaf: 1
                random_state: 42
                # Additional parameters can be added here
                max_features: auto
                bootstrap: true
                oob_score: false

  - name: model-eval
    stages:
      - name: "model-predict"
        tasks:
          - name: "score-model"
            params: {}
