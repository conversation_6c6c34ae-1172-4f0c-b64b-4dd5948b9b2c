random_seed: 0
data_base_path: data
log_base_path: logs

# Housing-specific configuration
housing:
  target_column: median_house_value
  max_house_value: 500000  # Cap for outlier handling
  test_size: 0.2

# Model configuration with p parameter support
model:
  default_algorithm: random_forest
  # P parameters for different algorithms
  p_parameters:
    random_forest:
      n_estimators: 100
      max_depth: null
      min_samples_split: 2
      min_samples_leaf: 1
      max_features: auto
      bootstrap: true
      oob_score: false
    linear_regression:
      fit_intercept: true
      normalize: false
      copy_X: true
    ols:
      # OLS-specific parameters can be added here
      alpha: 0.05

# Feature engineering configuration
feature_engineering:
  outlier_method: mean
  outlier_drop: false
  sampling_fraction: null
