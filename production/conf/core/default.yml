random_seed: 0
data_base_path: data
log_base_path: logs

# Housing-specific configuration
housing:
  target_column: median_house_value
  max_house_value: 500000  # Cap for outlier handling
  test_size: 0.2

# Model configuration
model:
  default_algorithm: random_forest
  random_forest:
    n_estimators: 100
    max_depth: null
    min_samples_split: 2
    min_samples_leaf: 1
  linear_regression:
    fit_intercept: true

# Feature engineering configuration
feature_engineering:
  outlier_method: mean
  outlier_drop: false
  sampling_fraction: null
