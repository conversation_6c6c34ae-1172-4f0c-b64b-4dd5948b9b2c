# MLflow Integration for Housing Price Prediction

This document describes the comprehensive MLflow integration added to the production pipeline for tracking experiments, metrics, parameters, and artifacts across all job stages.

## Overview

The MLflow integration provides:
- **Experiment Tracking**: Separate experiments for each job type
- **Metrics Logging**: Performance metrics, data quality metrics, and processing statistics
- **Parameter Logging**: Job parameters, model hyperparameters, and configuration settings
- **Artifact Logging**: Models, datasets, reports, and pipeline artifacts
- **Run Management**: Organized runs with tags and metadata
- **UI Access**: Web interface for exploring results

## Architecture

### Experiment Organization
- `housing-price-prediction-data-cleaning`: Data preprocessing experiments
- `housing-price-prediction-feature-engineering`: Feature engineering experiments  
- `housing-price-prediction-training`: Model training experiments
- `housing-price-prediction-evaluation`: Model evaluation experiments

### Key Components

1. **`mlflow_config.py`**: Centralized configuration for experiments and settings
2. **`mlflow_integration.py`**: Utility functions for logging and tracking
3. **Enhanced job processors**: All jobs now include MLflow tracking
4. **CLI extensions**: Commands for managing MLflow UI and experiments

## Usage

### Running Jobs with MLflow Tracking

#### Individual Jobs
```bash
# Run data cleaning with MLflow tracking
python -m production.cli job run -j data-cleaning

# Run feature engineering with MLflow tracking  
python -m production.cli job run -j feat-engg

# Run model training with MLflow tracking
python -m production.cli job run -j model-gen

# Run model evaluation with MLflow tracking
python -m production.cli job run -j model-eval
```

#### Complete Pipeline
```bash
# Run the complete pipeline with MLflow tracking
python production/run_pipeline_with_mlflow.py
```

### MLflow UI Management

#### Start MLflow UI
```bash
# Start MLflow UI (default: localhost:5000)
python -m production.cli mlflow ui

# Start on custom host/port
python -m production.cli mlflow ui --host 0.0.0.0 --port 8080
```

#### List Experiments and Runs
```bash
# List all experiments
python -m production.cli mlflow experiments

# List recent runs
python -m production.cli mlflow runs

# List runs for specific experiment
python -m production.cli mlflow runs --experiment-name housing-price-prediction-training
```

## Tracked Information

### Data Cleaning Jobs
- **Metrics**: Row counts, column counts, missing values, duplicates, memory usage
- **Parameters**: Input/output datasets, cleaning operations, transformation settings
- **Artifacts**: Dataset info files, processing logs

### Feature Engineering Jobs  
- **Metrics**: Feature counts, outlier treatment stats, transformation metrics
- **Parameters**: Outlier methods, imputation strategies, sampling settings
- **Artifacts**: Feature transformers, curated column lists, processing reports

### Model Training Jobs
- **Metrics**: Training MSE, RMSE, MAE, R², feature counts, sample sizes
- **Parameters**: Model algorithm, hyperparameters, random seeds, dataset paths
- **Artifacts**: Trained models, feature transformers, model reports

### Model Evaluation Jobs
- **Metrics**: Test MSE, RMSE, MAE, R², prediction statistics
- **Parameters**: Evaluation settings, model configuration
- **Artifacts**: Predictions, evaluation reports, performance summaries

## MLflow UI Features

### Experiment View
- Compare runs across different experiments
- Filter and sort runs by metrics or parameters
- View run details and artifacts

### Run Details
- Complete parameter and metric history
- Artifact browser with download capability
- Run comparison and analysis tools

### Model Registry
- Register and version trained models
- Track model lineage and metadata
- Deploy models to different stages

## Configuration

### Default Settings
- **Tracking URI**: Local file store in `artifacts/mlruns`
- **UI Host**: localhost
- **UI Port**: 5000
- **Artifact Location**: `artifacts/mlruns`

### Customization
Edit `production/mlflow_config.py` to modify:
- Experiment names and descriptions
- Artifact storage locations
- UI server settings
- Default tags and metadata

## Best Practices

### Run Organization
- Each job type has its own experiment for clear separation
- Runs are tagged with job name, stage, and processing type
- Timestamps and environment information are automatically logged

### Metric Naming
- Consistent naming convention: `{stage}_{metric_name}`
- Data quality metrics prefixed with dataset name
- Performance metrics clearly labeled (training vs test)

### Artifact Management
- Models saved with descriptive names and metadata
- Reports generated in human-readable formats
- Dataset information logged for reproducibility

## Troubleshooting

### Common Issues

1. **MLflow UI won't start**
   - Check if port 5000 is available
   - Verify artifact location exists and is writable
   - Try different port: `--port 8080`

2. **Experiments not showing**
   - Ensure jobs completed successfully
   - Check artifact location path
   - Verify MLflow initialization

3. **Missing metrics/artifacts**
   - Check job logs for errors
   - Verify file permissions in artifact directory
   - Ensure MLflow context is properly initialized

### Debug Commands
```bash
# Check MLflow version
python -c "import mlflow; print(mlflow.__version__)"

# Verify artifact location
ls -la artifacts/mlruns/

# Test MLflow connection
python -c "import mlflow; print(mlflow.get_tracking_uri())"
```

## Integration Benefits

1. **Experiment Reproducibility**: All parameters and settings logged
2. **Performance Monitoring**: Track metrics across runs and experiments  
3. **Artifact Management**: Centralized storage for models and reports
4. **Collaboration**: Shared experiment tracking and results
5. **Model Lifecycle**: Complete tracking from data to deployment
6. **Debugging**: Detailed logs and metrics for troubleshooting

## Next Steps

- Set up remote MLflow tracking server for team collaboration
- Integrate with model deployment pipelines
- Add automated model validation and testing
- Implement A/B testing framework with MLflow
- Set up alerts and monitoring for production models
