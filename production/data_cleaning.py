"""Processors for the data cleaning step of the worklow.

The processors in this step, apply the various cleaning steps identified
during EDA to create the training datasets.
"""
import mlflow
from sklearn.model_selection import StratifiedShuffleSplit

from ta_lib.core.api import (
    custom_train_test_split,
    load_dataset,
    register_processor,
    save_dataset
)
from production.scripts import binned_house_value
from production.mlflow_integration import (
    init_mlflow,
    log_data_quality_metrics,
    log_dataset_info,
    start_mlflow_run_with_tags,
    log_processing_time
)
from production.mlflow_config import get_experiment_name


@register_processor("data-cleaning", "housing")
def clean_housing_table(context, params):
    """Clean the ``HOUSING`` data table.

    The table contains information on housing features including location,
    age, rooms, population, income, and house values.
    """
    from datetime import datetime

    input_dataset = "raw/housing"
    output_dataset = "cleaned/housing"

    # Initialize MLflow for data cleaning
    experiment_name = get_experiment_name("data-cleaning")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="clean_housing_table",
        job_name="data-cleaning",
        stage_name="housing",
        dataset_type="housing"
    ):
        start_time = datetime.now()

        # Log parameters
        mlflow.log_param("input_dataset", input_dataset)
        mlflow.log_param("output_dataset", output_dataset)

        # load dataset
        housing_df = load_dataset(context, input_dataset)

        # Log input dataset info
        log_dataset_info(housing_df, "raw_housing")
        log_data_quality_metrics(housing_df, "raw_housing")

        # Store original columns for later use
        original_cols = housing_df.columns.tolist()
        mlflow.log_param("original_columns_count", len(original_cols))

        # Housing-specific data cleaning
        housing_df_clean = housing_df.copy()

        # Handle missing values in total_bedrooms (common issue in housing data)
        missing_bedrooms = housing_df_clean['total_bedrooms'].isna().sum()
        if missing_bedrooms > 0:
            # Fill missing bedrooms with median
            median_bedrooms = housing_df_clean['total_bedrooms'].median()
            housing_df_clean['total_bedrooms'] = housing_df_clean['total_bedrooms'].fillna(median_bedrooms)
            mlflow.log_param("missing_bedrooms_filled", missing_bedrooms)
            mlflow.log_param("bedrooms_fill_value", median_bedrooms)

        # Ensure no negative values in numeric columns
        numeric_cols = ['housing_median_age', 'total_rooms', 'total_bedrooms',
                       'population', 'households', 'median_income', 'median_house_value']

        for col in numeric_cols:
            if col in housing_df_clean.columns:
                negative_count = (housing_df_clean[col] < 0).sum()
                if negative_count > 0:
                    housing_df_clean[col] = housing_df_clean[col].clip(lower=0)
                    mlflow.log_param(f"negative_values_clipped_{col}", negative_count)

        # Handle outliers in median_house_value (cap at reasonable maximum)
        max_reasonable_value = 500000  # Cap at 500K as in original data
        outliers_capped = (housing_df_clean['median_house_value'] > max_reasonable_value).sum()
        housing_df_clean['median_house_value'] = housing_df_clean['median_house_value'].clip(upper=max_reasonable_value)

        if outliers_capped > 0:
            mlflow.log_param("house_value_outliers_capped", outliers_capped)
            mlflow.log_param("house_value_cap", max_reasonable_value)

        # Clean string columns (ocean_proximity)
        if 'ocean_proximity' in housing_df_clean.columns:
            housing_df_clean['ocean_proximity'] = housing_df_clean['ocean_proximity'].str.strip().str.upper()

        # Check for duplicates (unlikely in housing data but good practice)
        duplicates_before = housing_df_clean.duplicated().sum()
        housing_df_clean = housing_df_clean.drop_duplicates(keep='first')
        duplicates_removed = duplicates_before

        # Log cleaning metrics
        cleaning_metrics = {
            "duplicates_removed": duplicates_removed,
            "columns_after_cleaning": len(housing_df_clean.columns),
            "rows_after_cleaning": len(housing_df_clean),
            "missing_values_handled": missing_bedrooms
        }
        mlflow.log_metrics(cleaning_metrics)

        # Log output dataset info
        log_dataset_info(housing_df_clean, "cleaned_housing")
        log_data_quality_metrics(housing_df_clean, "cleaned_housing")

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "housing_cleaning")

        # save the dataset
        save_dataset(context, housing_df_clean, output_dataset)

        return housing_df_clean



@register_processor("data-cleaning", "train-test")
def create_training_datasets(context, params):
    """Split the ``HOUSING`` table into ``train`` and ``test`` datasets."""
    from datetime import datetime

    input_dataset = "cleaned/housing"
    output_train_features = "train/housing/features"
    output_train_target = "train/housing/target"
    output_test_features = "test/housing/features"
    output_test_target = "test/housing/target"

    # Initialize MLflow for data cleaning
    experiment_name = get_experiment_name("data-cleaning")
    init_mlflow(experiment_name)

    # Start MLflow run with tags
    with start_mlflow_run_with_tags(
        run_name="create_training_datasets",
        job_name="data-cleaning",
        stage_name="train-test",
        dataset_type="train_test_split"
    ):
        start_time = datetime.now()

        # Log parameters
        mlflow.log_param("input_dataset", input_dataset)
        mlflow.log_param("test_size", params["test_size"])
        mlflow.log_param("target_column", params["target"])
        mlflow.log_param("random_seed", context.random_seed)
        mlflow.log_param("split_strategy", "StratifiedShuffleSplit")

        # load dataset
        housing_df_processed = load_dataset(context, input_dataset)

        # Log input dataset info
        log_dataset_info(housing_df_processed, "input_housing")
        log_data_quality_metrics(housing_df_processed, "input_housing")

        # Create additional housing-specific features
        # Rooms per household
        housing_df_processed["rooms_per_household"] = (
            housing_df_processed["total_rooms"] / housing_df_processed["households"]
        )

        # Bedrooms per room ratio
        housing_df_processed["bedrooms_per_room"] = (
            housing_df_processed["total_bedrooms"] / housing_df_processed["total_rooms"]
        )

        # Population per household
        housing_df_processed["population_per_household"] = (
            housing_df_processed["population"] / housing_df_processed["households"]
        )

        # Log feature engineering metrics
        feature_metrics = {
            "features_added": 3,  # rooms_per_household, bedrooms_per_room, population_per_household
            "total_features": len(housing_df_processed.columns)
        }
        mlflow.log_metrics(feature_metrics)

        # split the data using stratified sampling based on house value
        splitter = StratifiedShuffleSplit(
            n_splits=1, test_size=params["test_size"], random_state=context.random_seed
        )
        housing_df_train, housing_df_test = custom_train_test_split(
            housing_df_processed, splitter, by=binned_house_value
        )

        # Log split metrics
        split_metrics = {
            "total_rows": len(housing_df_processed),
            "train_rows": len(housing_df_train),
            "test_rows": len(housing_df_test),
            "train_percentage": len(housing_df_train) / len(housing_df_processed) * 100,
            "test_percentage": len(housing_df_test) / len(housing_df_processed) * 100
        }
        mlflow.log_metrics(split_metrics)

        # split train dataset into features and target
        target_col = params["target"]
        train_X, train_y = (
            housing_df_train
            # split the dataset to train and test
            .get_features_targets(target_column_names=target_col)
        )

        # split test dataset into features and target
        test_X, test_y = (
            housing_df_test
            # split the dataset to train and test
            .get_features_targets(target_column_names=target_col)
        )

        # Log dataset info for all splits
        log_dataset_info(train_X, "train_features")
        log_dataset_info(train_y, "train_target")
        log_dataset_info(test_X, "test_features")
        log_dataset_info(test_y, "test_target")

        log_data_quality_metrics(train_X, "train_features")
        log_data_quality_metrics(test_X, "test_features")

        # save the datasets
        save_dataset(context, train_X, output_train_features)
        save_dataset(context, train_y, output_train_target)
        save_dataset(context, test_X, output_test_features)
        save_dataset(context, test_y, output_test_target)

        # Log processing time
        end_time = datetime.now()
        log_processing_time(start_time, end_time, "train_test_split")

        return {
            "train_shape": train_X.shape,
            "test_shape": test_X.shape,
            "target_column": target_col
        }
