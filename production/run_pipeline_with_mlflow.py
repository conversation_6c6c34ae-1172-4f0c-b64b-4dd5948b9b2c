#!/usr/bin/env python3
"""
<PERSON>ript to run the complete ML pipeline with MLflow tracking.
This script demonstrates how to run all jobs sequentially and view results in MLflow UI.
"""
import os
import sys
import subprocess
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from production.mlflow_config import get_mlflow_ui_url, get_artifact_location
from production.mlflow_integration import init_mlflow


def run_job(job_name, description=""):
    """Run a specific job using the CLI."""
    print(f"\n{'='*60}")
    print(f"Running Job: {job_name}")
    if description:
        print(f"Description: {description}")
    print(f"{'='*60}")
    
    cmd = [sys.executable, "-m", "production.cli", "job", "run", "-j", job_name]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Job completed successfully!")
        if result.stdout:
            print("Output:", result.stdout[-500:])  # Show last 500 chars
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Job failed with error: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout[-500:])
        if e.stderr:
            print("STDERR:", e.stderr[-500:])
        return False


def start_mlflow_ui_background():
    """Start MLflow UI in the background."""
    print("\n🚀 Starting MLflow UI in background...")
    
    artifact_location = get_artifact_location()
    ui_url = get_mlflow_ui_url()
    
    cmd = [
        sys.executable, "-m", "mlflow", "ui",
        "--backend-store-uri", f"file://{artifact_location}",
        "--host", "localhost",
        "--port", "5000"
    ]
    
    try:
        # Start MLflow UI in background
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Give it a moment to start
        time.sleep(3)
        
        # Check if it's still running
        if process.poll() is None:
            print(f"✅ MLflow UI started successfully!")
            print(f"🌐 Access the UI at: {ui_url}")
            return process
        else:
            print("❌ Failed to start MLflow UI")
            return None
            
    except Exception as e:
        print(f"❌ Error starting MLflow UI: {e}")
        return None


def main():
    """Run the complete ML pipeline with MLflow tracking."""
    print("🏠 Housing Price Prediction - MLflow Enhanced Pipeline")
    print("=" * 60)
    
    # Initialize MLflow
    print("🔧 Initializing MLflow...")
    init_mlflow()
    
    # Start MLflow UI
    ui_process = start_mlflow_ui_background()
    
    # Define the pipeline jobs
    jobs = [
        ("data-cleaning", "Clean and preprocess raw data"),
        ("feat-engg", "Engineer features for training"),
        ("model-gen", "Train the regression model"),
        ("model-eval", "Evaluate model performance")
    ]
    
    # Run each job
    success_count = 0
    for job_name, description in jobs:
        if run_job(job_name, description):
            success_count += 1
        else:
            print(f"\n❌ Pipeline stopped due to failure in job: {job_name}")
            break
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 PIPELINE SUMMARY")
    print(f"{'='*60}")
    print(f"Jobs completed successfully: {success_count}/{len(jobs)}")
    
    if success_count == len(jobs):
        print("🎉 All jobs completed successfully!")
        print("\n📈 MLflow Tracking Results:")
        print(f"   - View experiments at: {get_mlflow_ui_url()}")
        print("   - All job metrics, parameters, and artifacts are logged")
        print("   - Each job has its own experiment for organized tracking")
        
        # Show how to access results
        print(f"\n🔍 To explore results:")
        print(f"   1. Open your browser to: {get_mlflow_ui_url()}")
        print(f"   2. Browse experiments by job type:")
        print(f"      - housing-price-prediction-data-cleaning")
        print(f"      - housing-price-prediction-feature-engineering") 
        print(f"      - housing-price-prediction-training")
        print(f"      - housing-price-prediction-evaluation")
        print(f"   3. Compare runs and analyze metrics")
        
        print(f"\n📋 CLI Commands for MLflow:")
        print(f"   python -m production.cli mlflow ui          # Start MLflow UI")
        print(f"   python -m production.cli mlflow experiments # List experiments")
        print(f"   python -m production.cli mlflow runs        # List recent runs")
        
    else:
        print("❌ Pipeline completed with errors")
    
    # Keep MLflow UI running
    if ui_process and ui_process.poll() is None:
        print(f"\n🌐 MLflow UI is running at: {get_mlflow_ui_url()}")
        print("Press Ctrl+C to stop the UI and exit...")
        try:
            ui_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping MLflow UI...")
            ui_process.terminate()
            ui_process.wait()
            print("✅ MLflow UI stopped.")


if __name__ == "__main__":
    main()
