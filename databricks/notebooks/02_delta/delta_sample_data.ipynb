{"cells": [{"cell_type": "markdown", "source": ["##### Available data in raw form"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "1121bf2b-164d-40db-b19b-583beb37a9dc"}}}, {"cell_type": "code", "source": ["%sql\nCREATE DATABASE IF NOT EXISTS demo\n    LOCATION \"dbfs:/FileStore/code-templates/delta_data\""], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "e23ef290-98c8-436b-9f15-f897ec881e07"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"overflow": false, "datasetInfos": [], "data": [], "plotOptions": {"displayType": "table", "customPlotOptions": {}, "pivotColumns": null, "pivotAggregation": null, "xColumns": null, "yColumns": null}, "columnCustomDisplayInfos": {}, "aggType": "", "isJsonSchema": true, "removedWidgets": [], "aggSchema": [], "schema": [], "aggError": "", "aggData": [], "addedWidgets": {}, "dbfsResultPath": null, "type": "table", "aggOverflow": false, "aggSeriesLimitReached": false, "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .table-result-container {\n    max-height: 300px;\n    overflow: auto;\n  }\n  table, th, td {\n    border: 1px solid black;\n    border-collapse: collapse;\n  }\n  th, td {\n    padding: 5px;\n  }\n  th {\n    text-align: left;\n  }\n</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr></tr></thead><tbody></tbody></table></div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": ["%sql\nshow databases;"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "b4952564-acc4-4a0d-9862-d424083f71ec"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"overflow": false, "datasetInfos": [], "data": [["default"], ["demo"]], "plotOptions": {"displayType": "table", "customPlotOptions": {}, "pivotColumns": null, "pivotAggregation": null, "xColumns": null, "yColumns": null}, "columnCustomDisplayInfos": {}, "aggType": "", "isJsonSchema": true, "removedWidgets": [], "aggSchema": [], "schema": [{"name": "databaseName", "type": "\"string\"", "metadata": "{}"}], "aggError": "", "aggData": [], "addedWidgets": {}, "dbfsResultPath": null, "type": "table", "aggOverflow": false, "aggSeriesLimitReached": false, "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .table-result-container {\n    max-height: 300px;\n    overflow: auto;\n  }\n  table, th, td {\n    border: 1px solid black;\n    border-collapse: collapse;\n  }\n  th, td {\n    padding: 5px;\n  }\n  th {\n    text-align: left;\n  }\n</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>databaseName</th></tr></thead><tbody><tr><td>default</td></tr><tr><td>demo</td></tr></tbody></table></div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": ["%sql\nshow tables from demo;\n-- select * from demo.titanic_raw;\n-- select * from demo.his_tvr;"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "d980c6d1-fc78-41ac-a261-c6aa814681da"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"overflow": false, "datasetInfos": [], "data": [["demo", "boston", false], ["demo", "cancer", false], ["demo", "his_tvr", false], ["demo", "titanic_raw", false]], "plotOptions": {"displayType": "table", "customPlotOptions": {}, "pivotColumns": null, "pivotAggregation": null, "xColumns": null, "yColumns": null}, "columnCustomDisplayInfos": {}, "aggType": "", "isJsonSchema": true, "removedWidgets": [], "aggSchema": [], "schema": [{"name": "database", "type": "\"string\"", "metadata": "{}"}, {"name": "tableName", "type": "\"string\"", "metadata": "{}"}, {"name": "isTemporary", "type": "\"boolean\"", "metadata": "{}"}], "aggError": "", "aggData": [], "addedWidgets": {}, "dbfsResultPath": null, "type": "table", "aggOverflow": false, "aggSeriesLimitReached": false, "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .table-result-container {\n    max-height: 300px;\n    overflow: auto;\n  }\n  table, th, td {\n    border: 1px solid black;\n    border-collapse: collapse;\n  }\n  th, td {\n    padding: 5px;\n  }\n  th {\n    text-align: left;\n  }\n</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>database</th><th>tableName</th><th>isTemporary</th></tr></thead><tbody><tr><td>demo</td><td>boston</td><td>false</td></tr><tr><td>demo</td><td>cancer</td><td>false</td></tr><tr><td>demo</td><td>his_tvr</td><td>false</td></tr><tr><td>demo</td><td>titanic_raw</td><td>false</td></tr></tbody></table></div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": ["%sql\n-- DROP TABLE IF EXISTS demo.his_tvr"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "5d6e9661-55a4-4fde-8ce8-5b7050d03719"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"overflow": false, "datasetInfos": [], "data": [], "plotOptions": {"displayType": "table", "customPlotOptions": {}, "pivotColumns": null, "pivotAggregation": null, "xColumns": null, "yColumns": null}, "columnCustomDisplayInfos": {}, "aggType": "", "isJsonSchema": true, "removedWidgets": [], "aggSchema": [], "schema": [], "aggError": "", "aggData": [], "addedWidgets": {}, "dbfsResultPath": null, "type": "table", "aggOverflow": false, "aggSeriesLimitReached": false, "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .table-result-container {\n    max-height: 300px;\n    overflow: auto;\n  }\n  table, th, td {\n    border: 1px solid black;\n    border-collapse: collapse;\n  }\n  th, td {\n    padding: 5px;\n  }\n  th {\n    text-align: left;\n  }\n</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr></tr></thead><tbody></tbody></table></div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": ["# Removing data of a table\n# dbutils.fs.rm('dbfs:/FileStore/code-templates/delta_data/eda_data',recurse=True)"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "b9a8c269-67e0-460a-8170-4ace995cb08c"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">Out[6]: True</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">Out[6]: True</div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": ["dbutils.fs.ls(\"dbfs:/FileStore/code-templates/data\")"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "5e8677f3-bc3f-4ad5-b8a9-62d1ec33c82c"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">Out[1]: [FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/.gitkeep&#39;, name=&#39;.gitkeep&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/Titanic_Raw.csv&#39;, name=&#39;Titanic_Raw.csv&#39;, size=60302),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/cleaned/&#39;, name=&#39;cleaned/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/his_tvr.csv&#39;, name=&#39;his_tvr.csv&#39;, size=19370552),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/processed/&#39;, name=&#39;processed/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/pyspark_data/&#39;, name=&#39;pyspark_data/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/raw/&#39;, name=&#39;raw/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/sales/&#39;, name=&#39;sales/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/store_item_daily_predictions.csv&#39;, name=&#39;store_item_daily_predictions.csv&#39;, size=33537258),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/test/&#39;, name=&#39;test/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/train/&#39;, name=&#39;train/&#39;, size=0)]</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">Out[1]: [FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/.gitkeep&#39;, name=&#39;.gitkeep&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/Titanic_Raw.csv&#39;, name=&#39;Titanic_Raw.csv&#39;, size=60302),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/cleaned/&#39;, name=&#39;cleaned/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/his_tvr.csv&#39;, name=&#39;his_tvr.csv&#39;, size=19370552),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/processed/&#39;, name=&#39;processed/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/pyspark_data/&#39;, name=&#39;pyspark_data/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/raw/&#39;, name=&#39;raw/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/sales/&#39;, name=&#39;sales/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/store_item_daily_predictions.csv&#39;, name=&#39;store_item_daily_predictions.csv&#39;, size=33537258),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/test/&#39;, name=&#39;test/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/train/&#39;, name=&#39;train/&#39;, size=0)]</div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": ["# Imports\nimport pandas as pd"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "b5466ad5-b7c5-4536-bcc9-60e2c1b60f47"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\"></div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\"></div>"]}}], "execution_count": 0}, {"cell_type": "markdown", "source": ["##### AutoML\n* Sample data for AutoML example\n* dbfs:/FileStore/code-templates/data/Titanic_Raw.csv"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "f242003b-5a8d-4bb2-ac54-e90e7a9e4c5b"}}}, {"cell_type": "code", "source": ["# Reading in Pandas to preserve datatypes\nautoml_data_pd = pd.read_csv(\"/dbfs/FileStore/code-templates/data/Titanic_Raw.csv\")\nautoml_data = spark.createDataFrame(automl_data_pd)\nautoml_data.write.format(\"delta\").save(\"dbfs:/FileStore/code-templates/delta_data/automl_data\")\nspark.sql(\"CREATE TABLE demo.Titanic_Raw USING DELTA LOCATION 'dbfs:/FileStore/code-templates/delta_data/automl_data'\")"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "f98964b1-d9ec-4889-ac08-d2aeb7258990"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\"></div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\"></div>"]}}], "execution_count": 0}, {"cell_type": "markdown", "source": ["##### EDA\n* Sample data for EDA example\n* dbfs:/FileStore/code-templates/data/his_tvr.csv"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "23d61c55-5d35-4ee5-8049-9dbcf50aa761"}}}, {"cell_type": "code", "source": ["# Reading in Pandas to preserve datatypes\neda_data_pd = pd.read_csv(\"/dbfs/FileStore/code-templates/data/his_tvr.csv\", low_memory=False)\neda_data_pd = eda_data_pd.drop(columns = ['Unnamed: 0', 'Rch´000 {Av(Wg)}', 'user_rating'])\neda_data_pd[\"release_date\"] = pd.to_datetime(eda_data_pd['release_date'])\neda_data_pd[\"Date\"] = pd.to_datetime(eda_data_pd['Date'])\neda_data = spark.createDataFrame(eda_data_pd)\n\n# Spaces are not allowed in delta table header names\nfor col in eda_data.columns:\n  if \" \" in col:\n    eda_data = eda_data.withColumnRenamed(col, col.replace(\" \", \"_\"))\neda_data.write.format(\"delta\").mode(\"overwrite\").save(\"dbfs:/FileStore/code-templates/delta_data/eda_data\")\nspark.sql(\"CREATE TABLE demo.his_tvr USING DELTA LOCATION 'dbfs:/FileStore/code-templates/delta_data/eda_data'\")"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "4952fe0d-6a20-494b-abaf-3ab40ebda12d"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">Out[7]: DataFrame[]</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">Out[7]: DataFrame[]</div>"]}}], "execution_count": 0}, {"cell_type": "markdown", "source": ["##### Model Eval\n* Sample data for Model_Eval example\n* sklearn: load_boston, load_breast_cancer"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "ee0e27e6-4619-4398-9f5a-3349b690f5be"}}}, {"cell_type": "code", "source": ["from sklearn.datasets import load_boston, load_breast_cancer\n\n## Cancer Data: Binary Classification\ncancer_obj = load_breast_cancer()\ncancer = pd.DataFrame(cancer_obj['data'], columns=cancer_obj['feature_names'])\ncancer[\"target\"] = cancer_obj['target']\ncancer = spark.createDataFrame(cancer)\n\n# Spaces are not allowed in delta table header names\nfor col in cancer.columns:\n  if \" \" in col:\n    cancer = cancer.withColumnRenamed(col, col.replace(\" \", \"_\"))\ncancer.write.format(\"delta\").save(\"dbfs:/FileStore/code-templates/delta_data/cancer\")\nspark.sql(\"CREATE TABLE demo.cancer USING DELTA LOCATION 'dbfs:/FileStore/code-templates/delta_data/cancer'\")\n\n## Boston Data: Regression\nboston_obj = load_boston()\nboston = pd.DataFrame(boston_obj['data'], columns=boston_obj['feature_names'])\nboston[\"target\"] = boston_obj['target']\nboston = spark.createDataFrame(boston)\n\n# Spaces are not allowed in delta table header names\nfor col in boston.columns:\n  if \" \" in col:\n    boston = boston.withColumnRenamed(col, col.replace(\" \", \"_\"))\nboston.write.format(\"delta\").save(\"dbfs:/FileStore/code-templates/delta_data/boston\")\nspark.sql(\"CREATE TABLE demo.boston USING DELTA LOCATION 'dbfs:/FileStore/code-templates/delta_data/boston'\")"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "2e1d89b9-db61-4c47-84b2-f7dcfd1c591d"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\"></div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\"></div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": ["automl_data_pd.head()"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "b6a9b4bc-a811-4828-8712-631e397ced48"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">Out[15]: </div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">Out[15]: </div>"]}}, {"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>PassengerId</th>\n      <th>Survived</th>\n      <th>Pclass</th>\n      <th>Name</th>\n      <th>Sex</th>\n      <th>Age</th>\n      <th>SibSp</th>\n      <th>Parch</th>\n      <th>Ticket</th>\n      <th>Fare</th>\n      <th>Cabin</th>\n      <th>Embarked</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>0</td>\n      <td>3</td>\n      <td>Braund, Mr. <PERSON></td>\n      <td>male</td>\n      <td>22.0</td>\n      <td>1</td>\n      <td>0</td>\n      <td>A/5 21171</td>\n      <td>7.2500</td>\n      <td>NaN</td>\n      <td>S</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2</td>\n      <td>1</td>\n      <td>1</td>\n      <td>Cumings, Mrs. John Bradley (Florence Briggs Th...</td>\n      <td>female</td>\n      <td>38.0</td>\n      <td>1</td>\n      <td>0</td>\n      <td>PC 17599</td>\n      <td>71.2833</td>\n      <td>C85</td>\n      <td>C</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>3</td>\n      <td>1</td>\n      <td>3</td>\n      <td>Heikkinen, Miss. Laina</td>\n      <td>female</td>\n      <td>26.0</td>\n      <td>0</td>\n      <td>0</td>\n      <td>STON/O2. 3101282</td>\n      <td>7.9250</td>\n      <td>NaN</td>\n      <td>S</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>4</td>\n      <td>1</td>\n      <td>1</td>\n      <td>Futrelle, Mrs. Jacques Heath (Lily May Peel)</td>\n      <td>female</td>\n      <td>35.0</td>\n      <td>1</td>\n      <td>0</td>\n      <td>113803</td>\n      <td>53.1000</td>\n      <td>C123</td>\n      <td>S</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5</td>\n      <td>0</td>\n      <td>3</td>\n      <td>Allen, Mr. William Henry</td>\n      <td>male</td>\n      <td>35.0</td>\n      <td>0</td>\n      <td>0</td>\n      <td>373450</td>\n      <td>8.0500</td>\n      <td>NaN</td>\n      <td>S</td>\n    </tr>\n  </tbody>\n</table>\n</div>", "textData": null, "removedWidgets": [], "addedWidgets": {}, "type": "htmlSandbox", "arguments": {}}}, "data": {"text/html": ["<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>PassengerId</th>\n      <th>Survived</th>\n      <th>Pclass</th>\n      <th>Name</th>\n      <th>Sex</th>\n      <th>Age</th>\n      <th>SibSp</th>\n      <th>Parch</th>\n      <th>Ticket</th>\n      <th>Fare</th>\n      <th>Cabin</th>\n      <th>Embarked</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>0</td>\n      <td>3</td>\n      <td>Braund, Mr. <PERSON></td>\n      <td>male</td>\n      <td>22.0</td>\n      <td>1</td>\n      <td>0</td>\n      <td>A/5 21171</td>\n      <td>7.2500</td>\n      <td>NaN</td>\n      <td>S</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2</td>\n      <td>1</td>\n      <td>1</td>\n      <td>Cumings, Mrs. John Bradley (Florence Briggs Th...</td>\n      <td>female</td>\n      <td>38.0</td>\n      <td>1</td>\n      <td>0</td>\n      <td>PC 17599</td>\n      <td>71.2833</td>\n      <td>C85</td>\n      <td>C</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>3</td>\n      <td>1</td>\n      <td>3</td>\n      <td>Heikkinen, Miss. Laina</td>\n      <td>female</td>\n      <td>26.0</td>\n      <td>0</td>\n      <td>0</td>\n      <td>STON/O2. 3101282</td>\n      <td>7.9250</td>\n      <td>NaN</td>\n      <td>S</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>4</td>\n      <td>1</td>\n      <td>1</td>\n      <td>Futrelle, Mrs. Jacques Heath (Lily May Peel)</td>\n      <td>female</td>\n      <td>35.0</td>\n      <td>1</td>\n      <td>0</td>\n      <td>113803</td>\n      <td>53.1000</td>\n      <td>C123</td>\n      <td>S</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5</td>\n      <td>0</td>\n      <td>3</td>\n      <td>Allen, Mr. William Henry</td>\n      <td>male</td>\n      <td>35.0</td>\n      <td>0</td>\n      <td>0</td>\n      <td>373450</td>\n      <td>8.0500</td>\n      <td>NaN</td>\n      <td>S</td>\n    </tr>\n  </tbody>\n</table>\n</div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": [""], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "1d4b43db-ff05-4e56-bf99-760d0e82faa4"}}, "outputs": [], "execution_count": 0}], "metadata": {"application/vnd.databricks.v1+notebook": {"notebookName": "delta_sample_data", "dashboards": [], "notebookMetadata": {"pythonIndentUnit": 2}, "language": "python", "widgets": {}, "notebookOrigID": 1998322810472307}}, "nbformat": 4, "nbformat_minor": 0}