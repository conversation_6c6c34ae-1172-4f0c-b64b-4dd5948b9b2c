{"cells": [{"cell_type": "code", "source": ["# Stack location : ls\ndbutils.fs.ls(\"dbfs:/FileStore/code-templates/\")"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "*************-4d54-ac7b-56a0f7b8658c"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">Out[1]: [FileInfo(path=&#39;dbfs:/FileStore/code-templates/.git/&#39;, name=&#39;.git/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/README.md&#39;, name=&#39;README.md&#39;, size=9902),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/TigerML/&#39;, name=&#39;TigerML/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/ct_stack_setup/&#39;, name=&#39;ct_stack_setup/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/&#39;, name=&#39;data/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/delta_data/&#39;, name=&#39;delta_data/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/deploy/&#39;, name=&#39;deploy/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/logs/&#39;, name=&#39;logs/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/mlflow_artifacts/&#39;, name=&#39;mlflow_artifacts/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/mlruns/&#39;, name=&#39;mlruns/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/notebooks_config/&#39;, name=&#39;notebooks_config/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/output_reports/&#39;, name=&#39;output_reports/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/production/&#39;, name=&#39;production/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/production_config/&#39;, name=&#39;production_config/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/scripts/&#39;, name=&#39;scripts/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/setup.cfg&#39;, name=&#39;setup.cfg&#39;, size=587),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/setup.py&#39;, name=&#39;setup.py&#39;, size=799),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/src/&#39;, name=&#39;src/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/tasks.py&#39;, name=&#39;tasks.py&#39;, size=24063)]</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">Out[1]: [FileInfo(path=&#39;dbfs:/FileStore/code-templates/.git/&#39;, name=&#39;.git/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/README.md&#39;, name=&#39;README.md&#39;, size=9902),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/TigerML/&#39;, name=&#39;TigerML/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/ct_stack_setup/&#39;, name=&#39;ct_stack_setup/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/data/&#39;, name=&#39;data/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/delta_data/&#39;, name=&#39;delta_data/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/deploy/&#39;, name=&#39;deploy/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/logs/&#39;, name=&#39;logs/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/mlflow_artifacts/&#39;, name=&#39;mlflow_artifacts/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/mlruns/&#39;, name=&#39;mlruns/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/notebooks_config/&#39;, name=&#39;notebooks_config/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/output_reports/&#39;, name=&#39;output_reports/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/production/&#39;, name=&#39;production/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/production_config/&#39;, name=&#39;production_config/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/scripts/&#39;, name=&#39;scripts/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/setup.cfg&#39;, name=&#39;setup.cfg&#39;, size=587),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/setup.py&#39;, name=&#39;setup.py&#39;, size=799),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/src/&#39;, name=&#39;src/&#39;, size=0),\n FileInfo(path=&#39;dbfs:/FileStore/code-templates/tasks.py&#39;, name=&#39;tasks.py&#39;, size=24063)]</div>"]}}], "execution_count": 0}, {"cell_type": "markdown", "source": ["### Setting up Python Environment\n##### INIT script\n* It takes ~12 mins to start the cluster using this init script\n* Please make sure init file should be written using dbutils.fs.put command only otherwise it can cause 'fi' syntax issue"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "614cb795-df6f-4cc5-8674-7894ade885b1"}}}, {"cell_type": "code", "source": ["dbutils.fs.put(\"dbfs:/FileStore/Git/init.sh\",\"\"\"\n#!/bin/bash\necho $DB_IS_DRIVER\nif [[ $DB_IS_DRIVER = \"TRUE\" ]]; then\n  # Installing packages from conda\n  /databricks/python/bin/python -V\n  . /databricks/conda/etc/profile.d/conda.sh\n  conda activate /databricks/python\n  conda install --yes --file /dbfs/FileStore/Git/code-templates/deploy/conda_envs/linux-cpu-64-dev.lock\n  # Installing packages from PIP\n  pip install -r /dbfs/FileStore/Git/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt \n  # Installing Code-Templates\n  cd /dbfs/FileStore/Git/code-templates\n  pip install -e .\nelse\n  /databricks/python/bin/pip install invoke==1.3.0\nfi\n\"\"\", True)"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "3cee3d52-21b8-4bd7-a55a-d5b28675eb57"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">Wrote 611 bytes.\nOut[23]: True</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">Wrote 611 bytes.\nOut[23]: True</div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": ["%sh\ncat /dbfs/FileStore/Git/init.sh"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "e0a60b15-3e99-46d4-8d1c-3d1fd7ad41f4"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">\n#!/bin/bash\necho $DB_IS_DRIVER\nif [[ $DB_IS_DRIVER = &#34;TRUE&#34; ]]; then\n  # Installing Invoke package\n  /databricks/python/bin/pip install invoke==1.3.0\n  /databricks/python/bin/pip install luminol==0.4\n  /databricks/python/bin/pip install ruptures==1.1.3\n  # Installing packages from conda\n  /databricks/python/bin/python -V\n  . /databricks/conda/etc/profile.d/conda.sh\n  conda activate /databricks/python\n  conda install --yes --file /dbfs/FileStore/Git/code-templates/deploy/conda_envs/linux-cpu-64-dev.lock\n  # Installing packages from PIP\n  pip install -r /dbfs/FileStore/Git/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt \n  # Installing Code-Templates\n  cd /dbfs/FileStore/Git/code-templates\n  pip install -e .\n  # Installing TigerML\n  cd /dbfs/FileStore/Git/TigerML/python/\n  pip install -e .\nelse\n  /databricks/python/bin/pip install invoke==1.3.0\nfi\n</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">\n#!/bin/bash\necho $DB_IS_DRIVER\nif [[ $DB_IS_DRIVER = &#34;TRUE&#34; ]]; then\n  # Installing Invoke package\n  /databricks/python/bin/pip install invoke==1.3.0\n  /databricks/python/bin/pip install luminol==0.4\n  /databricks/python/bin/pip install ruptures==1.1.3\n  # Installing packages from conda\n  /databricks/python/bin/python -V\n  . /databricks/conda/etc/profile.d/conda.sh\n  conda activate /databricks/python\n  conda install --yes --file /dbfs/FileStore/Git/code-templates/deploy/conda_envs/linux-cpu-64-dev.lock\n  # Installing packages from PIP\n  pip install -r /dbfs/FileStore/Git/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt \n  # Installing Code-Templates\n  cd /dbfs/FileStore/Git/code-templates\n  pip install -e .\n  # Installing TigerML\n  cd /dbfs/FileStore/Git/TigerML/python/\n  pip install -e .\nelse\n  /databricks/python/bin/pip install invoke==1.3.0\nfi\n</div>"]}}], "execution_count": 0}, {"cell_type": "markdown", "source": ["##### Checking INIT Logs\n\nInitiate logging in cluster advance setting to view this"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "c6ac3dd2-9082-4d90-93b8-0ea2e24fce77"}}}, {"cell_type": "code", "source": ["%sh\n# INIT file log\nls /dbfs/cluster-logs/0427-081739-mushy682/init_scripts/0427-081739-mushy682_10_139_64_4"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "50f5f0e3-492e-4ff3-8cba-4399515deeba"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">20210427_102637_00_init.sh.stderr.log\n20210427_102637_00_init.sh.stdout.log\n20210427_103827_00_init.sh.stderr.log\n20210427_103827_00_init.sh.stdout.log\n20210427_104122_00_init.sh.stderr.log\n20210427_104122_00_init.sh.stdout.log\n20210427_104519_00_init.sh.stderr.log\n20210427_104519_00_init.sh.stdout.log\n20210427_110027_00_init.sh.stderr.log\n20210427_110027_00_init.sh.stdout.log\n20210427_110514_00_init.sh.stderr.log\n20210427_110514_00_init.sh.stdout.log\n20210427_110551_00_init.sh.stderr.log\n20210427_110551_00_init.sh.stdout.log\n20210427_163056_00_init.sh.stderr.log\n20210427_163056_00_init.sh.stdout.log\n</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">20210427_102637_00_init.sh.stderr.log\n20210427_102637_00_init.sh.stdout.log\n20210427_103827_00_init.sh.stderr.log\n20210427_103827_00_init.sh.stdout.log\n20210427_104122_00_init.sh.stderr.log\n20210427_104122_00_init.sh.stdout.log\n20210427_104519_00_init.sh.stderr.log\n20210427_104519_00_init.sh.stdout.log\n20210427_110027_00_init.sh.stderr.log\n20210427_110027_00_init.sh.stdout.log\n20210427_110514_00_init.sh.stderr.log\n20210427_110514_00_init.sh.stdout.log\n20210427_110551_00_init.sh.stderr.log\n20210427_110551_00_init.sh.stdout.log\n20210427_163056_00_init.sh.stderr.log\n20210427_163056_00_init.sh.stdout.log\n</div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": ["%sh\ncat /dbfs/cluster-logs/0427-081739-mushy682/init_scripts/0427-081739-mushy682_10_139_64_4/20210427_163056_00_init.sh.stderr.log"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "7e051add-8c2e-4228-828c-0cf7af85b3e8"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\"></div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\"></div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": ["%sh\ntail -f /dbfs/cluster-logs/0427-081739-mushy682/init_scripts/0427-081739-mushy682_10_139_64_4/20210427_163056_00_init.sh.stdout.log"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "533a1075-fda8-413f-8366-4931deb7911d"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">\nshap-0.35.0          |            |   0% \nshap-0.35.0          | ########## | 100% \n\nstatsmodels-0.11.1   |            |   0% \nstatsmodels-0.11.1   | ########## | 100% \n\ntpot-0.11.5          |            |   0% \ntpot-0.11.5          | ########## | 100% \n\nyellowbrick-1.1      |            |   0% \nyellowbrick-1.1      | ########## | 100% \n\ncategory_encoders-2. |            |   0% \ncategory_encoders-2. | ########## | 100% \n\npyjanitor-0.20.9     |            |   0% \npyjanitor-0.20.9     | ########## | 100% \n\npymc3-3.9.3          |            |   0% \npymc3-3.9.3          | ########## | 100% \nPreparing transaction: ...working... done\nVerifying transaction: ...working... done\nExecuting transaction: ...working... done\n</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">\nshap-0.35.0          |            |   0% \nshap-0.35.0          | ########## | 100% \n\nstatsmodels-0.11.1   |            |   0% \nstatsmodels-0.11.1   | ########## | 100% \n\ntpot-0.11.5          |            |   0% \ntpot-0.11.5          | ########## | 100% \n\nyellowbrick-1.1      |            |   0% \nyellowbrick-1.1      | ########## | 100% \n\ncategory_encoders-2. |            |   0% \ncategory_encoders-2. | ########## | 100% \n\npyjanitor-0.20.9     |            |   0% \npyjanitor-0.20.9     | ########## | 100% \n\npymc3-3.9.3          |            |   0% \npymc3-3.9.3          | ########## | 100% \nPreparing transaction: ...working... done\nVerifying transaction: ...working... done\nExecuting transaction: ...working... done\n</div>"]}}, {"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"type": "ipynbError", "data": "", "errorSummary": "Cancelled", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>"]}}], "execution_count": 0}, {"cell_type": "markdown", "source": ["## Specific commands of INIT"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "1bf4eedb-a1b4-463b-b8be-5909aeecd56f"}}}, {"cell_type": "markdown", "source": ["###### Command to install PIP packages"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "cffc2001-33c2-4c31-b896-1dc40d66a19e"}}}, {"cell_type": "code", "source": ["%sh\n# Testing invoke commands\npip install -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt "], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "10e679f6-ae7d-40ae-a98e-7762d1c63584"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">Collecting git+https://github.com/jupyter/jupyter-sphinx.git@v0.3.1 (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31))\n  Cloning https://github.com/jupyter/jupyter-sphinx.git (to revision v0.3.1) to /tmp/pip-req-build-kqr18817\nRequirement already satisfied (use --upgrade to upgrade): jupyter-sphinx==0.3.1 from git+https://github.com/jupyter/jupyter-sphinx.git@v0.3.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31))\nRequirement already satisfied: great-expectations==0.13.4 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.13.4)\nRequirement already satisfied: xverse==1.0.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (1.0.5)\nRequirement already satisfied: cloudpickle==1.3.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 3)) (1.3.0)\nProcessing /root/.cache/pip/wheels/3a/34/2d/d78c1a3c467d348ed40de462777bb69f61dd3176ee940409fc/gast-0.3.2-py3-none-any.whl\nRequirement already satisfied: absl-py==0.10.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 5)) (0.10.0)\nRequirement already satisfied: astunparse==1.6.3 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 6)) (1.6.3)\nRequirement already satisfied: google-pasta==0.2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 7)) (0.2.0)\nRequirement already satisfied: grpcio==1.32.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 8)) (1.32.0)\nRequirement already satisfied: keras-preprocessing==1.1.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 9)) (1.1.2)\nRequirement already satisfied: opt-einsum==3.3.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 10)) (3.3.0)\nRequirement already satisfied: protobuf==3.13.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 11)) (3.13.0)\nRequirement already satisfied: pyasn1==0.4.8 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 12)) (0.4.8)\nRequirement already satisfied: rsa==4.6 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 13)) (4.6)\nRequirement already satisfied: pyasn1-modules==0.2.8 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 14)) (0.2.8)\nRequirement already satisfied: google-auth==1.22.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 15)) (1.22.1)\nRequirement already satisfied: google-auth-oauthlib==0.4.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 16)) (0.4.1)\nRequirement already satisfied: oauthlib==3.1.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 17)) (3.1.0)\nRequirement already satisfied: requests-oauthlib==1.3.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 18)) (1.3.0)\nRequirement already satisfied: tensorboard-plugin-wit==1.7.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 20)) (1.7.0)\nRequirement already satisfied: werkzeug==1.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 21)) (1.0.1)\nRequirement already satisfied: wheel==0.35.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 22)) (0.35.1)\nRequirement already satisfied: tensorflow-estimator==2.2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 23)) (2.2.0)\nRequirement already satisfied: termcolor==1.1.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 24)) (1.1.0)\nRequirement already satisfied: wrapt==1.12.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 25)) (1.12.1)\nRequirement already satisfied: tensorboard==2.2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 26)) (2.2.0)\nRequirement already satisfied: tensorflow==2.2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 27)) (2.2.0)\nRequirement already satisfied: tensorflow-probability==0.10.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 28)) (0.10.1)\nRequirement already satisfied: sphinx-rtd-theme==0.5.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 29)) (0.5.0)\nRequirement already satisfied: jupyter-core==4.6.3 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 30)) (4.6.3)\nRequirement already satisfied: ipywidgets==7.5.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 32)) (7.5.1)\nRequirement already satisfied: jupyter-sphinx==0.3.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 33)) (0.3.1)\nRequirement already satisfied: widgetsnbextension==3.5.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (3.5.1)\nRequirement already satisfied: iniconfig==1.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 35)) (1.0.1)\nRequirement already satisfied: flake8-bandit==2.1.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (2.1.2)\nRequirement already satisfied: flake8-polyfill==1.0.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 37)) (1.0.2)\nRequirement already satisfied: jupyterlab-server==1.2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 38)) (1.2.0)\nRequirement already satisfied: graphviz==0.14 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 39)) (0.14)\nRequirement already satisfied: update-checker==0.18.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 40)) (0.18.0)\nRequirement already satisfied: Sphinx&gt;=2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (3.5.4)\nRequirement already satisfied: IPython in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (7.19.0)\nRequirement already satisfied: nbconvert&gt;=5.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (6.0.7)\nRequirement already satisfied: nbformat in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (5.1.2)\nRequirement already satisfied: jinja2&gt;=2.10 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.11.2)\nRequirement already satisfied: importlib-metadata&gt;=1.7.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.0.0)\nRequirement already satisfied: mistune&gt;=0.8.4 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.8.4)\nRequirement already satisfied: pytz&gt;=2015.6 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2020.5)\nRequirement already satisfied: jsonpatch&gt;=1.22 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.32)\nRequirement already satisfied: pyparsing&lt;3,&gt;=2.4 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.4.7)\nRequirement already satisfied: python-dateutil&gt;=2.8.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.8.1)\nRequirement already satisfied: requests&lt;2.24,&gt;=2.20 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.23.0)\nRequirement already satisfied: tzlocal&gt;=1.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.1)\nRequirement already satisfied: altair&lt;5,&gt;=4.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (4.1.0)\nRequirement already satisfied: ruamel.yaml&gt;=0.16 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.17.4)\nRequirement already satisfied: Click&gt;=7.1.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (7.1.2)\nRequirement already satisfied: numpy&gt;=1.14.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.19.2)\nRequirement already satisfied: jsonschema&gt;=2.5.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (3.2.0)\nRequirement already satisfied: pandas&gt;=0.23.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.1.3)\nRequirement already satisfied: scipy&gt;=0.19.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.4.1)\nRequirement already satisfied: statsmodels&gt;=0.6.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (0.12.0)\nRequirement already satisfied: matplotlib&gt;=3.0.3 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (3.2.2)\nRequirement already satisfied: scikit-learn&gt;=0.19.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (0.23.2)\nRequirement already satisfied: six in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from absl-py==0.10.0-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 5)) (1.15.0)\nRequirement already satisfied: setuptools in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from protobuf==3.13.0-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 11)) (50.3.1.post20201107)\nRequirement already satisfied: cachetools&lt;5.0,&gt;=2.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from google-auth==1.22.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 15)) (4.2.1)\nRequirement already satisfied: markdown&gt;=2.6.8 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from tensorboard==2.2.0-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 26)) (3.3.3)\nRequirement already satisfied: h5py&lt;2.11.0,&gt;=2.10.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from tensorflow==2.2.0-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 27)) (2.10.0)\nRequirement already satisfied: decorator in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from tensorflow-probability==0.10.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 28)) (4.4.2)\nRequirement already satisfied: traitlets in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyter-core==4.6.3-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 30)) (5.0.5)\nRequirement already satisfied: ipykernel&gt;=4.5.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from ipywidgets==7.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 32)) (5.3.4)\nRequirement already satisfied: notebook&gt;=4.4.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (6.3.0)\nRequirement already satisfied: bandit in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (1.7.0)\nRequirement already satisfied: pycodestyle in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (2.7.0)\nRequirement already satisfied: flake8 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (3.9.1)\nRequirement already satisfied: json5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyterlab-server==1.2.0-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 38)) (0.9.5)\nRequirement already satisfied: Pygments&gt;=2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (2.7.2)\nRequirement already satisfied: sphinxcontrib-jsmath in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.0.1)\nRequirement already satisfied: sphinxcontrib-qthelp in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.0.3)\nRequirement already satisfied: babel&gt;=1.3 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (2.9.0)\nRequirement already satisfied: alabaster&lt;0.8,&gt;=0.7 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.7.12)\nRequirement already satisfied: snowballstemmer&gt;=1.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (2.1.0)\nRequirement already satisfied: docutils&lt;0.17,&gt;=0.12 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.15.2)\nRequirement already satisfied: imagesize in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.2.0)\nRequirement already satisfied: sphinxcontrib-devhelp in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.0.2)\nRequirement already satisfied: sphinxcontrib-serializinghtml in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.1.4)\nRequirement already satisfied: sphinxcontrib-htmlhelp in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.0.3)\nRequirement already satisfied: sphinxcontrib-applehelp in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.0.2)\nRequirement already satisfied: packaging in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (20.4)\nRequirement already satisfied: jedi&gt;=0.10 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.17.2)\nRequirement already satisfied: backcall in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.2.0)\nRequirement already satisfied: pexpect&gt;4.3; sys_platform != &#34;win32&#34; in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (4.8.0)\nRequirement already satisfied: pickleshare in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.7.5)\nRequirement already satisfied: prompt-toolkit!=3.0.0,!=3.0.1,&lt;3.1.0,&gt;=2.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (3.0.8)\nRequirement already satisfied: bleach in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (3.3.0)\nRequirement already satisfied: defusedxml in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.7.1)\nRequirement already satisfied: jupyterlab-pygments in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.1.2)\nRequirement already satisfied: entrypoints&gt;=0.2.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.3)\nRequirement already satisfied: nbclient&lt;0.6.0,&gt;=0.5.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.5.3)\nRequirement already satisfied: pandocfilters&gt;=1.4.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.4.3)\nRequirement already satisfied: testpath in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.4.4)\nRequirement already satisfied: ipython-genutils in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbformat-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.2.0)\nRequirement already satisfied: MarkupSafe&gt;=0.23 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jinja2&gt;=2.10-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.1.1)\nRequirement already satisfied: zipp&gt;=0.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from importlib-metadata&gt;=1.7.0-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (3.4.0)\nRequirement already satisfied: jsonpointer&gt;=1.9 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jsonpatch&gt;=1.22-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.1)\nRequirement already satisfied: chardet&lt;4,&gt;=3.0.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from requests&lt;2.24,&gt;=2.20-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (3.0.4)\nRequirement already satisfied: idna&lt;3,&gt;=2.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from requests&lt;2.24,&gt;=2.20-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.10)\nRequirement already satisfied: urllib3!=1.25.0,!=1.25.1,&lt;1.26,&gt;=1.21.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from requests&lt;2.24,&gt;=2.20-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.25.11)\nRequirement already satisfied: certifi&gt;=2017.4.17 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from requests&lt;2.24,&gt;=2.20-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2020.12.5)\nRequirement already satisfied: toolz in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from altair&lt;5,&gt;=4.0.0-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.11.1)\nRequirement already satisfied: ruamel.yaml.clib&gt;=0.1.2; platform_python_implementation == &#34;CPython&#34; and python_version &lt; &#34;3.10&#34; in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from ruamel.yaml&gt;=0.16-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.2.2)\nRequirement already satisfied: attrs&gt;=17.4.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jsonschema&gt;=2.5.1-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (20.3.0)\nRequirement already satisfied: pyrsistent&gt;=0.14.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jsonschema&gt;=2.5.1-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.17.3)\nRequirement already satisfied: patsy&gt;=0.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from statsmodels&gt;=0.6.1-&gt;xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (0.5.1)\nRequirement already satisfied: cycler&gt;=0.10 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from matplotlib&gt;=3.0.3-&gt;xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (0.10.0)\nRequirement already satisfied: kiwisolver&gt;=1.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from matplotlib&gt;=3.0.3-&gt;xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (1.3.0)\nRequirement already satisfied: threadpoolctl&gt;=2.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from scikit-learn&gt;=0.19.0-&gt;xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (2.1.0)\nRequirement already satisfied: joblib&gt;=0.11 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from scikit-learn&gt;=0.19.0-&gt;xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (0.17.0)\nRequirement already satisfied: jupyter-client in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from ipykernel&gt;=4.5.1-&gt;ipywidgets==7.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 32)) (6.1.7)\nRequirement already satisfied: tornado&gt;=4.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from ipykernel&gt;=4.5.1-&gt;ipywidgets==7.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 32)) (6.1)\nRequirement already satisfied: terminado&gt;=0.8.3 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (0.9.4)\nRequirement already satisfied: prometheus-client in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (0.9.0)\nRequirement already satisfied: argon2-cffi in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (20.1.0)\nRequirement already satisfied: Send2Trash&gt;=1.5.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (1.5.0)\nRequirement already satisfied: pyzmq&gt;=17 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (19.0.2)\nRequirement already satisfied: GitPython&gt;=1.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (3.1.12)\nRequirement already satisfied: stevedore&gt;=1.20.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (3.3.0)\nRequirement already satisfied: PyYAML&gt;=5.3.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (5.4.1)\nRequirement already satisfied: mccabe&lt;0.7.0,&gt;=0.6.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from flake8-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (0.6.1)\nRequirement already satisfied: pyflakes&lt;2.4.0,&gt;=2.3.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from flake8-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (2.3.1)\nRequirement already satisfied: parso&lt;0.8.0,&gt;=0.7.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jedi&gt;=0.10-&gt;IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.7.0)\nRequirement already satisfied: ptyprocess&gt;=0.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from pexpect&gt;4.3; sys_platform != &#34;win32&#34;-&gt;IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.6.0)\nRequirement already satisfied: wcwidth in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from prompt-toolkit!=3.0.0,!=3.0.1,&lt;3.1.0,&gt;=2.0.0-&gt;IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.2.5)\nRequirement already satisfied: webencodings in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from bleach-&gt;nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.5.1)\nRequirement already satisfied: nest-asyncio in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbclient&lt;0.6.0,&gt;=0.5.0-&gt;nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.5.1)\nRequirement already satisfied: async-generator in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbclient&lt;0.6.0,&gt;=0.5.0-&gt;nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.10)\nRequirement already satisfied: cffi&gt;=1.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from argon2-cffi-&gt;notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (1.14.3)\nRequirement already satisfied: gitdb&lt;5,&gt;=4.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from GitPython&gt;=1.0.1-&gt;bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (4.0.5)\nRequirement already satisfied: pbr!=2.1.0,&gt;=2.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from stevedore&gt;=1.20.0-&gt;bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (5.6.0)\nRequirement already satisfied: pycparser in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from cffi&gt;=1.0.0-&gt;argon2-cffi-&gt;notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (2.20)\nRequirement already satisfied: smmap&lt;4,&gt;=3.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from gitdb&lt;5,&gt;=4.0.1-&gt;GitPython&gt;=1.0.1-&gt;bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (3.0.5)\nInstalling collected packages: gast\n  Attempting uninstall: gast\n    Found existing installation: gast 0.3.3\n    Uninstalling gast-0.3.3:\n      Successfully uninstalled gast-0.3.3\nERROR: After October 2020 you may experience errors when installing or updating packages. This is because pip will change the way that it resolves dependency conflicts.\n\nWe recommend you use --use-feature=2020-resolver to test your packages with the new resolver before it becomes the default.\n\ntensorflow 2.2.0 requires gast==0.3.3, but you&#39;ll have gast 0.3.2 which is incompatible.\ntensorflow-cpu 2.4.1 requires gast==0.3.3, but you&#39;ll have gast 0.3.2 which is incompatible.\ntensorflow-cpu 2.4.1 requires tensorboard~=2.4, but you&#39;ll have tensorboard 2.2.0 which is incompatible.\ntensorflow-cpu 2.4.1 requires tensorflow-estimator&lt;2.5.0,&gt;=2.4.0, but you&#39;ll have tensorflow-estimator 2.2.0 which is incompatible.\nSuccessfully installed gast-0.3.2\n</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">Collecting git+https://github.com/jupyter/jupyter-sphinx.git@v0.3.1 (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31))\n  Cloning https://github.com/jupyter/jupyter-sphinx.git (to revision v0.3.1) to /tmp/pip-req-build-kqr18817\nRequirement already satisfied (use --upgrade to upgrade): jupyter-sphinx==0.3.1 from git+https://github.com/jupyter/jupyter-sphinx.git@v0.3.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31))\nRequirement already satisfied: great-expectations==0.13.4 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.13.4)\nRequirement already satisfied: xverse==1.0.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (1.0.5)\nRequirement already satisfied: cloudpickle==1.3.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 3)) (1.3.0)\nProcessing /root/.cache/pip/wheels/3a/34/2d/d78c1a3c467d348ed40de462777bb69f61dd3176ee940409fc/gast-0.3.2-py3-none-any.whl\nRequirement already satisfied: absl-py==0.10.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 5)) (0.10.0)\nRequirement already satisfied: astunparse==1.6.3 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 6)) (1.6.3)\nRequirement already satisfied: google-pasta==0.2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 7)) (0.2.0)\nRequirement already satisfied: grpcio==1.32.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 8)) (1.32.0)\nRequirement already satisfied: keras-preprocessing==1.1.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 9)) (1.1.2)\nRequirement already satisfied: opt-einsum==3.3.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 10)) (3.3.0)\nRequirement already satisfied: protobuf==3.13.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 11)) (3.13.0)\nRequirement already satisfied: pyasn1==0.4.8 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 12)) (0.4.8)\nRequirement already satisfied: rsa==4.6 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 13)) (4.6)\nRequirement already satisfied: pyasn1-modules==0.2.8 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 14)) (0.2.8)\nRequirement already satisfied: google-auth==1.22.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 15)) (1.22.1)\nRequirement already satisfied: google-auth-oauthlib==0.4.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 16)) (0.4.1)\nRequirement already satisfied: oauthlib==3.1.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 17)) (3.1.0)\nRequirement already satisfied: requests-oauthlib==1.3.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 18)) (1.3.0)\nRequirement already satisfied: tensorboard-plugin-wit==1.7.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 20)) (1.7.0)\nRequirement already satisfied: werkzeug==1.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 21)) (1.0.1)\nRequirement already satisfied: wheel==0.35.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 22)) (0.35.1)\nRequirement already satisfied: tensorflow-estimator==2.2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 23)) (2.2.0)\nRequirement already satisfied: termcolor==1.1.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 24)) (1.1.0)\nRequirement already satisfied: wrapt==1.12.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 25)) (1.12.1)\nRequirement already satisfied: tensorboard==2.2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 26)) (2.2.0)\nRequirement already satisfied: tensorflow==2.2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 27)) (2.2.0)\nRequirement already satisfied: tensorflow-probability==0.10.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 28)) (0.10.1)\nRequirement already satisfied: sphinx-rtd-theme==0.5.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 29)) (0.5.0)\nRequirement already satisfied: jupyter-core==4.6.3 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 30)) (4.6.3)\nRequirement already satisfied: ipywidgets==7.5.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 32)) (7.5.1)\nRequirement already satisfied: jupyter-sphinx==0.3.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 33)) (0.3.1)\nRequirement already satisfied: widgetsnbextension==3.5.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (3.5.1)\nRequirement already satisfied: iniconfig==1.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 35)) (1.0.1)\nRequirement already satisfied: flake8-bandit==2.1.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (2.1.2)\nRequirement already satisfied: flake8-polyfill==1.0.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 37)) (1.0.2)\nRequirement already satisfied: jupyterlab-server==1.2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 38)) (1.2.0)\nRequirement already satisfied: graphviz==0.14 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 39)) (0.14)\nRequirement already satisfied: update-checker==0.18.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from -r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 40)) (0.18.0)\nRequirement already satisfied: Sphinx&gt;=2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (3.5.4)\nRequirement already satisfied: IPython in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (7.19.0)\nRequirement already satisfied: nbconvert&gt;=5.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (6.0.7)\nRequirement already satisfied: nbformat in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (5.1.2)\nRequirement already satisfied: jinja2&gt;=2.10 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.11.2)\nRequirement already satisfied: importlib-metadata&gt;=1.7.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.0.0)\nRequirement already satisfied: mistune&gt;=0.8.4 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.8.4)\nRequirement already satisfied: pytz&gt;=2015.6 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2020.5)\nRequirement already satisfied: jsonpatch&gt;=1.22 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.32)\nRequirement already satisfied: pyparsing&lt;3,&gt;=2.4 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.4.7)\nRequirement already satisfied: python-dateutil&gt;=2.8.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.8.1)\nRequirement already satisfied: requests&lt;2.24,&gt;=2.20 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.23.0)\nRequirement already satisfied: tzlocal&gt;=1.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.1)\nRequirement already satisfied: altair&lt;5,&gt;=4.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (4.1.0)\nRequirement already satisfied: ruamel.yaml&gt;=0.16 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.17.4)\nRequirement already satisfied: Click&gt;=7.1.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (7.1.2)\nRequirement already satisfied: numpy&gt;=1.14.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.19.2)\nRequirement already satisfied: jsonschema&gt;=2.5.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (3.2.0)\nRequirement already satisfied: pandas&gt;=0.23.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.1.3)\nRequirement already satisfied: scipy&gt;=0.19.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.4.1)\nRequirement already satisfied: statsmodels&gt;=0.6.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (0.12.0)\nRequirement already satisfied: matplotlib&gt;=3.0.3 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (3.2.2)\nRequirement already satisfied: scikit-learn&gt;=0.19.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (0.23.2)\nRequirement already satisfied: six in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from absl-py==0.10.0-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 5)) (1.15.0)\nRequirement already satisfied: setuptools in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from protobuf==3.13.0-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 11)) (50.3.1.post20201107)\nRequirement already satisfied: cachetools&lt;5.0,&gt;=2.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from google-auth==1.22.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 15)) (4.2.1)\nRequirement already satisfied: markdown&gt;=2.6.8 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from tensorboard==2.2.0-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 26)) (3.3.3)\nRequirement already satisfied: h5py&lt;2.11.0,&gt;=2.10.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from tensorflow==2.2.0-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 27)) (2.10.0)\nRequirement already satisfied: decorator in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from tensorflow-probability==0.10.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 28)) (4.4.2)\nRequirement already satisfied: traitlets in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyter-core==4.6.3-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 30)) (5.0.5)\nRequirement already satisfied: ipykernel&gt;=4.5.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from ipywidgets==7.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 32)) (5.3.4)\nRequirement already satisfied: notebook&gt;=4.4.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (6.3.0)\nRequirement already satisfied: bandit in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (1.7.0)\nRequirement already satisfied: pycodestyle in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (2.7.0)\nRequirement already satisfied: flake8 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (3.9.1)\nRequirement already satisfied: json5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jupyterlab-server==1.2.0-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 38)) (0.9.5)\nRequirement already satisfied: Pygments&gt;=2.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (2.7.2)\nRequirement already satisfied: sphinxcontrib-jsmath in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.0.1)\nRequirement already satisfied: sphinxcontrib-qthelp in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.0.3)\nRequirement already satisfied: babel&gt;=1.3 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (2.9.0)\nRequirement already satisfied: alabaster&lt;0.8,&gt;=0.7 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.7.12)\nRequirement already satisfied: snowballstemmer&gt;=1.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (2.1.0)\nRequirement already satisfied: docutils&lt;0.17,&gt;=0.12 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.15.2)\nRequirement already satisfied: imagesize in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.2.0)\nRequirement already satisfied: sphinxcontrib-devhelp in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.0.2)\nRequirement already satisfied: sphinxcontrib-serializinghtml in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.1.4)\nRequirement already satisfied: sphinxcontrib-htmlhelp in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.0.3)\nRequirement already satisfied: sphinxcontrib-applehelp in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.0.2)\nRequirement already satisfied: packaging in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from Sphinx&gt;=2-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (20.4)\nRequirement already satisfied: jedi&gt;=0.10 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.17.2)\nRequirement already satisfied: backcall in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.2.0)\nRequirement already satisfied: pexpect&gt;4.3; sys_platform != &#34;win32&#34; in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (4.8.0)\nRequirement already satisfied: pickleshare in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.7.5)\nRequirement already satisfied: prompt-toolkit!=3.0.0,!=3.0.1,&lt;3.1.0,&gt;=2.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (3.0.8)\nRequirement already satisfied: bleach in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (3.3.0)\nRequirement already satisfied: defusedxml in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.7.1)\nRequirement already satisfied: jupyterlab-pygments in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.1.2)\nRequirement already satisfied: entrypoints&gt;=0.2.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.3)\nRequirement already satisfied: nbclient&lt;0.6.0,&gt;=0.5.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.5.3)\nRequirement already satisfied: pandocfilters&gt;=1.4.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.4.3)\nRequirement already satisfied: testpath in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.4.4)\nRequirement already satisfied: ipython-genutils in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbformat-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.2.0)\nRequirement already satisfied: MarkupSafe&gt;=0.23 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jinja2&gt;=2.10-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.1.1)\nRequirement already satisfied: zipp&gt;=0.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from importlib-metadata&gt;=1.7.0-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (3.4.0)\nRequirement already satisfied: jsonpointer&gt;=1.9 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jsonpatch&gt;=1.22-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.1)\nRequirement already satisfied: chardet&lt;4,&gt;=3.0.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from requests&lt;2.24,&gt;=2.20-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (3.0.4)\nRequirement already satisfied: idna&lt;3,&gt;=2.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from requests&lt;2.24,&gt;=2.20-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2.10)\nRequirement already satisfied: urllib3!=1.25.0,!=1.25.1,&lt;1.26,&gt;=1.21.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from requests&lt;2.24,&gt;=2.20-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (1.25.11)\nRequirement already satisfied: certifi&gt;=2017.4.17 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from requests&lt;2.24,&gt;=2.20-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (2020.12.5)\nRequirement already satisfied: toolz in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from altair&lt;5,&gt;=4.0.0-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.11.1)\nRequirement already satisfied: ruamel.yaml.clib&gt;=0.1.2; platform_python_implementation == &#34;CPython&#34; and python_version &lt; &#34;3.10&#34; in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from ruamel.yaml&gt;=0.16-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.2.2)\nRequirement already satisfied: attrs&gt;=17.4.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jsonschema&gt;=2.5.1-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (20.3.0)\nRequirement already satisfied: pyrsistent&gt;=0.14.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jsonschema&gt;=2.5.1-&gt;great-expectations==0.13.4-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 1)) (0.17.3)\nRequirement already satisfied: patsy&gt;=0.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from statsmodels&gt;=0.6.1-&gt;xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (0.5.1)\nRequirement already satisfied: cycler&gt;=0.10 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from matplotlib&gt;=3.0.3-&gt;xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (0.10.0)\nRequirement already satisfied: kiwisolver&gt;=1.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from matplotlib&gt;=3.0.3-&gt;xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (1.3.0)\nRequirement already satisfied: threadpoolctl&gt;=2.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from scikit-learn&gt;=0.19.0-&gt;xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (2.1.0)\nRequirement already satisfied: joblib&gt;=0.11 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from scikit-learn&gt;=0.19.0-&gt;xverse==1.0.5-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 2)) (0.17.0)\nRequirement already satisfied: jupyter-client in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from ipykernel&gt;=4.5.1-&gt;ipywidgets==7.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 32)) (6.1.7)\nRequirement already satisfied: tornado&gt;=4.2 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from ipykernel&gt;=4.5.1-&gt;ipywidgets==7.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 32)) (6.1)\nRequirement already satisfied: terminado&gt;=0.8.3 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (0.9.4)\nRequirement already satisfied: prometheus-client in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (0.9.0)\nRequirement already satisfied: argon2-cffi in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (20.1.0)\nRequirement already satisfied: Send2Trash&gt;=1.5.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (1.5.0)\nRequirement already satisfied: pyzmq&gt;=17 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (19.0.2)\nRequirement already satisfied: GitPython&gt;=1.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (3.1.12)\nRequirement already satisfied: stevedore&gt;=1.20.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (3.3.0)\nRequirement already satisfied: PyYAML&gt;=5.3.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (5.4.1)\nRequirement already satisfied: mccabe&lt;0.7.0,&gt;=0.6.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from flake8-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (0.6.1)\nRequirement already satisfied: pyflakes&lt;2.4.0,&gt;=2.3.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from flake8-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (2.3.1)\nRequirement already satisfied: parso&lt;0.8.0,&gt;=0.7.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from jedi&gt;=0.10-&gt;IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.7.0)\nRequirement already satisfied: ptyprocess&gt;=0.5 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from pexpect&gt;4.3; sys_platform != &#34;win32&#34;-&gt;IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.6.0)\nRequirement already satisfied: wcwidth in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from prompt-toolkit!=3.0.0,!=3.0.1,&lt;3.1.0,&gt;=2.0.0-&gt;IPython-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.2.5)\nRequirement already satisfied: webencodings in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from bleach-&gt;nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (0.5.1)\nRequirement already satisfied: nest-asyncio in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbclient&lt;0.6.0,&gt;=0.5.0-&gt;nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.5.1)\nRequirement already satisfied: async-generator in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from nbclient&lt;0.6.0,&gt;=0.5.0-&gt;nbconvert&gt;=5.5-&gt;jupyter-sphinx==0.3.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 31)) (1.10)\nRequirement already satisfied: cffi&gt;=1.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from argon2-cffi-&gt;notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (1.14.3)\nRequirement already satisfied: gitdb&lt;5,&gt;=4.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from GitPython&gt;=1.0.1-&gt;bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (4.0.5)\nRequirement already satisfied: pbr!=2.1.0,&gt;=2.0.0 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from stevedore&gt;=1.20.0-&gt;bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (5.6.0)\nRequirement already satisfied: pycparser in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from cffi&gt;=1.0.0-&gt;argon2-cffi-&gt;notebook&gt;=4.4.1-&gt;widgetsnbextension==3.5.1-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 34)) (2.20)\nRequirement already satisfied: smmap&lt;4,&gt;=3.0.1 in /databricks/conda/envs/databricks-ml/lib/python3.8/site-packages (from gitdb&lt;5,&gt;=4.0.1-&gt;GitPython&gt;=1.0.1-&gt;bandit-&gt;flake8-bandit==2.1.2-&gt;-r /dbfs/FileStore/code-templates/deploy/conda_envs/requirements-linux-cpu-64-dev.txt (line 36)) (3.0.5)\nInstalling collected packages: gast\n  Attempting uninstall: gast\n    Found existing installation: gast 0.3.3\n    Uninstalling gast-0.3.3:\n      Successfully uninstalled gast-0.3.3\nERROR: After October 2020 you may experience errors when installing or updating packages. This is because pip will change the way that it resolves dependency conflicts.\n\nWe recommend you use --use-feature=2020-resolver to test your packages with the new resolver before it becomes the default.\n\ntensorflow 2.2.0 requires gast==0.3.3, but you&#39;ll have gast 0.3.2 which is incompatible.\ntensorflow-cpu 2.4.1 requires gast==0.3.3, but you&#39;ll have gast 0.3.2 which is incompatible.\ntensorflow-cpu 2.4.1 requires tensorboard~=2.4, but you&#39;ll have tensorboard 2.2.0 which is incompatible.\ntensorflow-cpu 2.4.1 requires tensorflow-estimator&lt;2.5.0,&gt;=2.4.0, but you&#39;ll have tensorflow-estimator 2.2.0 which is incompatible.\nSuccessfully installed gast-0.3.2\n</div>"]}}], "execution_count": 0}, {"cell_type": "markdown", "source": ["###### Command to install Conda packages"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "56314464-95ad-4c52-8a54-d65930c219ff"}}}, {"cell_type": "code", "source": ["%sh\n/databricks/python/bin/python -V\n. /databricks/conda/etc/profile.d/conda.sh\nconda activate /databricks/python\nconda install --yes --file /dbfs/FileStore/code-templates/deploy/conda_envs/linux-cpu-64-dev.lock"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "1b1dd953-48f8-4c54-9fff-5056d3535506"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">Python 3.7.7\nPreparing transaction: ...working... done\nVerifying transaction: ...working... done\nExecuting transaction: ...working... done\n</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">Python 3.7.7\nPreparing transaction: ...working... done\nVerifying transaction: ...working... done\nExecuting transaction: ...working... done\n</div>"]}}], "execution_count": 0}, {"cell_type": "markdown", "source": ["###### Command to install code-template(ta_lib) packages"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "217e3a75-f4ab-4f1d-9e36-28e4f1ad1bff"}}}, {"cell_type": "code", "source": ["%sh\ncd /dbfs/FileStore/code-templates\npip install -e ."], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "df164926-a06c-442d-a34a-532d569af8d2"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">Obtaining file:///dbfs/FileStore/code-templates\nInstalling collected packages: ta-lib\n  Attempting uninstall: ta-lib\n    Found existing installation: ta-lib 1.0.0\n    Uninstalling ta-lib-1.0.0:\n      Successfully uninstalled ta-lib-1.0.0\n  Running setup.py develop for ta-lib\nSuccessfully installed ta-lib\n</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">Obtaining file:///dbfs/FileStore/code-templates\nInstalling collected packages: ta-lib\n  Attempting uninstall: ta-lib\n    Found existing installation: ta-lib 1.0.0\n    Uninstalling ta-lib-1.0.0:\n      Successfully uninstalled ta-lib-1.0.0\n  Running setup.py develop for ta-lib\nSuccessfully installed ta-lib\n</div>"]}}], "execution_count": 0}, {"cell_type": "markdown", "source": ["###### Command to install TigerML packages"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "07d91a13-529c-448f-9e5f-efef633802ff"}}}, {"cell_type": "code", "source": ["%sh\n# ls /dbfs/FileStore/code-templates/TigerML\ncd /dbfs/FileStore/code-templates/TigerML\npip install -e ."], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "25189c17-91a7-4e88-af80-28a7fac00842"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">Obtaining file:///dbfs/FileStore/code-templates/TigerML\nInstalling collected packages: tigerml\n  Running setup.py develop for tigerml\nSuccessfully installed tigerml\n</div>", "removedWidgets": [], "addedWidgets": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">Obtaining file:///dbfs/FileStore/code-templates/TigerML\nInstalling collected packages: tigerml\n  Running setup.py develop for tigerml\nSuccessfully installed tigerml\n</div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": [""], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "36d76257-95f6-4285-87d2-cba9fe706244"}}, "outputs": [], "execution_count": 0}], "metadata": {"application/vnd.databricks.v1+notebook": {"notebookName": "env_setup", "dashboards": [], "notebookMetadata": {"pythonIndentUnit": 2}, "language": "python", "widgets": {}, "notebookOrigID": 4289473657480741}}, "nbformat": 4, "nbformat_minor": 0}