{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "a9c6c895-03de-4995-a7c3-64d56362f2eb", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .table-result-container {\n", "    max-height: 300px;\n", "    overflow: auto;\n", "  }\n", "  table, th, td {\n", "    border: 1px solid black;\n", "    border-collapse: collapse;\n", "  }\n", "  th, td {\n", "    padding: 5px;\n", "  }\n", "  th {\n", "    text-align: left;\n", "  }\n", "</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>database</th><th>tableName</th><th>isTemporary</th></tr></thead><tbody><tr><td>demo</td><td>boston</td><td>false</td></tr><tr><td>demo</td><td>cancer</td><td>false</td></tr><tr><td>demo</td><td>his_tvr</td><td>false</td></tr><tr><td>demo</td><td>titanic_raw</td><td>false</td></tr></tbody></table></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "aggData": [], "aggError": "", "aggOverflow": false, "aggSchema": [], "aggSeriesLimitReached": false, "aggType": "", "arguments": {}, "columnCustomDisplayInfos": {}, "data": [["demo", "boston", false], ["demo", "cancer", false], ["demo", "his_tvr", false], ["demo", "titanic_raw", false]], "datasetInfos": [], "dbfsResultPath": null, "isJsonSchema": true, "overflow": false, "plotOptions": {"customPlotOptions": {}, "displayType": "table", "pivotAggregation": null, "pivotColumns": [], "xColumns": [], "yColumns": []}, "removedWidgets": [], "schema": [{"metadata": "{}", "name": "database", "type": "\"string\""}, {"metadata": "{}", "name": "tableName", "type": "\"string\""}, {"metadata": "{}", "name": "isTemporary", "type": "\"boolean\""}], "type": "table"}}, "output_type": "display_data"}], "source": ["%sql\n", "show tables from demo;\n", "-- select * from demo.titanic_raw;\n", "-- select * from demo.his_tvr;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "863f83e2-70b6-49ec-a9fe-0b1d64789897", "showTitle": false, "title": ""}}, "source": ["### EDA"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "6ae5a4ce-b9d5-415e-8fb8-a4c3cc39cc11", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\"></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\"></div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["import ta_lib"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "c8cdaafc-1a3d-4bfa-bccf-817f8f881397", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">/databricks/python/lib/python3.7/site-packages/sklearn/utils/deprecation.py:143: FutureWarning: The sklearn.metrics.classification module is  deprecated in version 0.22 and will be removed in version 0.24. The corresponding classes / functions should instead be imported from sklearn.metrics. Anything that cannot be imported from sklearn.metrics is now part of the private API.\n", "  warnings.warn(message, FutureWarning)\n", "/databricks/spark/python/pyspark/sql/pandas/conversion.py:92: UserWarning: to<PERSON><PERSON><PERSON> attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n", "  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\n", "Attempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n", "  warnings.warn(msg)\n", "/databricks/python/lib/python3.7/site-packages/scipy/stats/morestats.py:1681: UserWarning: p-value may not be accurate for N &gt; 5000.\n", "  warnings.warn(&#34;p-value may not be accurate for N &gt; 5000.&#34;)\n", "/dbfs/FileStore/Git/code-templates/src/ta_lib/_vendor/tigerml/core/dataframe/base.py:40: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  self._data.__setitem__(key, value)\n", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">/databricks/python/lib/python3.7/site-packages/sklearn/utils/deprecation.py:143: FutureWarning: The sklearn.metrics.classification module is  deprecated in version 0.22 and will be removed in version 0.24. The corresponding classes / functions should instead be imported from sklearn.metrics. Anything that cannot be imported from sklearn.metrics is now part of the private API.\n  warnings.warn(message, FutureWarning)\n/databricks/spark/python/pyspark/sql/pandas/conversion.py:92: UserWarning: to<PERSON><PERSON><PERSON> attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\nAttempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n  warnings.warn(msg)\n/databricks/python/lib/python3.7/site-packages/scipy/stats/morestats.py:1681: UserWarning: p-value may not be accurate for N &gt; 5000.\n  warnings.warn(&#34;p-value may not be accurate for N &gt; 5000.&#34;)\n/dbfs/FileStore/Git/code-templates/src/ta_lib/_vendor/tigerml/core/dataframe/base.py:40: SettingWithCopyWarning: \nA value is trying to be set on a copy of a slice from a DataFrame\n\nSee the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n  self._data.__setitem__(key, value)\n</div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["# EDAReport\n", "import pandas as pd\n", "from tigerml.eda import EDAReport\n", "\n", "df = spark.sql('select * from demo.his_tvr').to<PERSON><PERSON><PERSON>()\n", "an = EDAReport(df, y='TVR')\n", "\n", "# Download this file to local by running following command in local machine, make sure databricks CLI is configured using token\n", "# databricks fs cp dbfs:/FileStore/code-templates/output_reports/eda_report.html <target_path_in_local_without_filename/.>\n", "an.get_report(save_path = \"/dbfs/FileStore/code-templates/output_reports/eda_report.html\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "3b6ca890-d226-4ebc-a777-2454a4a03d6f", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">/databricks/spark/python/pyspark/sql/pandas/conversion.py:92: UserWarning: toPandas attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n", "  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\n", "Attempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n", "  warnings.warn(msg)\n", "/dbfs/FileStore/Git/code-templates/src/ta_lib/_vendor/tigerml/core/dataframe/base.py:40: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  self._data.__setitem__(key, value)\n", "/databricks/python/lib/python3.7/site-packages/pandas/core/indexing.py:671: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  self._setitem_with_indexer(indexer, value)\n", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">/databricks/spark/python/pyspark/sql/pandas/conversion.py:92: UserWarning: to<PERSON><PERSON><PERSON> attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\nAttempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n  warnings.warn(msg)\n/dbfs/FileStore/Git/code-templates/src/ta_lib/_vendor/tigerml/core/dataframe/base.py:40: SettingWithCopyWarning: \nA value is trying to be set on a copy of a slice from a DataFrame\n\nSee the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n  self._data.__setitem__(key, value)\n/databricks/python/lib/python3.7/site-packages/pandas/core/indexing.py:671: SettingWithCopyWarning: \nA value is trying to be set on a copy of a slice from a DataFrame\n\nSee the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n  self._setitem_with_indexer(indexer, value)\n</div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["# Segmented\n", "import pandas as pd\n", "from tigerml.eda import SegmentedEDAReport\n", "\n", "df = spark.sql('select * from demo.his_tvr').to<PERSON><PERSON><PERSON>()\n", "an = SegmentedEDAReport(df, segment_by=['Channel'], y='TVR')\n", "\n", "# Download this file to local by running following command in local machine, make sure databricks CLI is configured using token\n", "# databricks fs cp dbfs:/FileStore/code-templates/output_reports/segmented_eda_report.html <target_path_in_local_without_filename/.>\n", "an.get_report(y='TVR', quick=True, corr_threshold=None, save_path = \"/dbfs/FileStore/code-templates/output_reports/segmented_eda_report.html\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "aeb313cb-057c-41c3-baf9-876c887f13e2", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">/databricks/spark/python/pyspark/sql/pandas/conversion.py:92: UserWarning: toPandas attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n", "  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\n", "Attempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n", "  warnings.warn(msg)\n", "/databricks/python/lib/python3.7/site-packages/scipy/stats/morestats.py:1681: UserWarning: p-value may not be accurate for N &gt; 5000.\n", "  warnings.warn(&#34;p-value may not be accurate for N &gt; 5000.&#34;)\n", "/dbfs/FileStore/Git/code-templates/src/ta_lib/_vendor/tigerml/core/dataframe/base.py:40: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  self._data.__setitem__(key, value)\n", "/databricks/python/lib/python3.7/site-packages/statsmodels/tsa/stattools.py:572: FutureWarning: fft=True will become the default in a future version of statsmodels. To suppress this warning, explicitly set fft=False.\n", "  FutureWarning\n", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">/databricks/spark/python/pyspark/sql/pandas/conversion.py:92: UserWarning: to<PERSON><PERSON><PERSON> attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\nAttempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n  warnings.warn(msg)\n/databricks/python/lib/python3.7/site-packages/scipy/stats/morestats.py:1681: UserWarning: p-value may not be accurate for N &gt; 5000.\n  warnings.warn(&#34;p-value may not be accurate for N &gt; 5000.&#34;)\n/dbfs/FileStore/Git/code-templates/src/ta_lib/_vendor/tigerml/core/dataframe/base.py:40: SettingWithCopyWarning: \nA value is trying to be set on a copy of a slice from a DataFrame\n\nSee the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n  self._data.__setitem__(key, value)\n/databricks/python/lib/python3.7/site-packages/statsmodels/tsa/stattools.py:572: FutureWarning: fft=True will become the default in a future version of statsmodels. To suppress this warning, explicitly set fft=False.\n  FutureWarning\n</div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["# Timeseries\n", "import pandas as pd\n", "from tigerml.eda import TSReport\n", "\n", "df = spark.sql('select * from demo.his_tvr').to<PERSON><PERSON><PERSON>()\n", "an = TSReport(df, ts_column='Date', y='TVR')\n", "\n", "# Download this file to local by running following command in local machine, make sure databricks CLI is configured using token\n", "# databricks fs cp dbfs:/FileStore/code-templates/output_reports/ts_eda_report.html <target_path_in_local_without_filename/.>\n", "an.get_report(y='TVR', quick=True, corr_threshold=None, save_path = \"/dbfs/FileStore/code-templates/output_reports/ts_eda_report.html\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "01260921-e791-4a32-91fe-e7e99b5ccf72", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">/databricks/spark/python/pyspark/sql/pandas/conversion.py:92: UserWarning: toPandas attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n", "  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\n", "Attempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n", "  warnings.warn(msg)\n", "/dbfs/FileStore/Git/code-templates/src/ta_lib/_vendor/tigerml/core/dataframe/base.py:40: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  self._data.__setitem__(key, value)\n", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">/databricks/spark/python/pyspark/sql/pandas/conversion.py:92: UserWarning: to<PERSON><PERSON><PERSON> attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\nAttempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n  warnings.warn(msg)\n/dbfs/FileStore/Git/code-templates/src/ta_lib/_vendor/tigerml/core/dataframe/base.py:40: SettingWithCopyWarning: \nA value is trying to be set on a copy of a slice from a DataFrame\n\nSee the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n  self._data.__setitem__(key, value)\n</div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["#SegmentedTSReport\n", "import pandas as pd\n", "from tigerml.eda import SegmentedTSReport\n", "\n", "df = spark.sql('select * from demo.his_tvr').to<PERSON><PERSON><PERSON>()\n", "an_seg = SegmentedTSReport(df, ts_column='Date', ts_identifiers=['Channel'], y='TVR')\n", "\n", "# Download this file to local by running following command in local machine, make sure databricks CLI is configured using token\n", "# databricks fs cp dbfs:/FileStore/code-templates/output_reports/segmented_ts_eda_report.html <target_path_in_local_without_filename/.>\n", "an_seg.get_report(y='TVR', quick=True, corr_threshold=None, save_path = \"/dbfs/FileStore/code-templates/output_reports/segmented_ts_eda_report.html\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "c5aeabbd-4d67-44e8-a9c0-b6197181d08e", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>"]}, "metadata": {"application/vnd.databricks.v1+output": {"arguments": {}, "data": "", "errorSummary": "", "type": "ipynbError"}}, "output_type": "display_data"}], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "language": "python", "notebookMetadata": {"pythonIndentUnit": 2}, "notebookName": "02_EDA", "notebookOrigID": 3802253373203151, "widgets": {}}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 1}