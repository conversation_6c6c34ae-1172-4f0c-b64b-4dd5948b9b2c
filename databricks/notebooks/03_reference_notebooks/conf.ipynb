{"cells": [{"cell_type": "code", "source": ["import ta_lib\nfrom tigerml.core.utils import DictObject\ncore = DictObject({\n    \"random_seed\": 0,\n    \"data_base_path\": \"/dbfs/mnt/code_templates/regression-py/data\",\n    \"reports_path\": \"/dbfs/mnt/code_templates/Reports\"\n})\n\nlogging = DictObject({\n    \"version\": 1,\n    \"disable_existing_loggers\": False,\n    \"formatters\": {\n        \"default\": {\n            \"format\": \"%(asctime)s — %(name)s — %(levelname)s — %(funcName)s:%(lineno)d — %(message)s\",\n            \"datefmt\": \"%Y-%m-%d %H:%M:%S\",\n        },\n        \"simple\": {\"format\": \"%(message)s\"},\n    },\n    \"handlers\": {\n        \"console_handler\": {\n            \"class\": \"logging.StreamHandler\",\n            \"level\": \"DEBUG\",\n            \"formatter\": \"simple\",\n        }\n    },\n    \"root\": {\"level\": \"WARN\", \"handlers\": [\"console_handler\"], \"propogate\": True},\n    \"loggers\": {\n        \"ta_lib\": {\n            \"level\": \"WARN\",\n            \"handlers\": [\"console_handler\"],\n            \"propogate\": True,\n        }\n    },\n})\n\ndata_catalog = DictObject({\n    \"datasets\": {\n        \"raw\": {\n                \"orders\": {\n                    \"type\": \"ds\",\n                    \"format\": \"csv\",\n                    \"uri\": f\"{core.data_base_path}/raw/sales/orders.csv\",\n                    \"driver_params\": {},\n                },\n                \"product\": {\n                    \"type\": \"ds\",\n                    \"format\": \"csv\",\n                    \"uri\": f\"{core.data_base_path}/raw/sales/prod_master.csv\",\n                    \"driver_params\": {},\n                },\n            },\n        \"cleaned\": {\n            \"orders\": {\n                \"type\": \"ds\",\n                \"format\": \"parquet\",\n                \"uri\": f\"{core.data_base_path}/cleaned/sales/orders.parquet\",\n                \"driver_params\": {},\n            },\n            \"product\": {\n                \"type\": \"ds\",\n                \"format\": \"parquet\",\n                \"uri\": f\"{core.data_base_path}/cleaned/sales/product.parquet\",\n                \"driver_params\": {},\n            },\n            \"sales\": {\n                \"type\": \"ds\",\n                \"format\": \"parquet\",\n                \"uri\": f\"{core.data_base_path}/cleaned/sales/sales.parquet\",\n                \"driver_params\": {},\n            },\n        },\n        \"processed\": {\n            \"sales\": {\n                \"type\": \"ds\",\n                \"format\": \"parquet\",\n                \"uri\": f\"{core.data_base_path}/processed/sales/sales.parquet\",\n                \"driver_params\": {},\n            }\n        },\n        \"train\": {\n            \"sales\": {\n                \"features\": {\n                    \"type\": \"ds\",\n                    \"format\": \"parquet\",\n                    \"uri\": f\"{core.data_base_path}/train/sales/features.parquet\",\n                    \"driver_params\": {\"save\": {\"index\": False}},\n                },\n                \"target\": {\n                    \"type\": \"ds\",\n                    \"format\": \"parquet\",\n                    \"uri\": f\"{core.data_base_path}/train/sales/target.parquet\",\n                    \"driver_params\": {\"save\": {\"index\": False}},\n                },\n            }\n        },\n        \"test\": {\n            \"sales\": {\n                \"features\": {\n                    \"type\": \"ds\",\n                    \"format\": \"parquet\",\n                    \"uri\": f\"{core.data_base_path}/test/sales/features.parquet\",\n                    \"driver_params\": {\"save\": {\"index\": False}},\n                },\n                \"target\": {\n                    \"type\": \"ds\",\n                    \"format\": \"parquet\",\n                    \"uri\": f\"{core.data_base_path}/test/sales/target.parquet\",\n                    \"driver_params\": {\"save\": {\"index\": False}},\n                },\n            }\n        },\n        \"score\": {\n            \"sales\": {\n                \"output\": {\n                    \"type\": \"ds\",\n                    \"format\": \"parquet\",\n                    \"uri\": f\"{core.data_base_path}/test/sales/scored_output.parquet\",\n                    \"driver_params\": {\"save\": {\"index\": False}},\n                }\n            }\n        },\n    }\n})"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "b7ae8472-03d0-4b92-8684-3c280fab1691"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\"></div>", "removedWidgets": [], "addedWidgets": {}, "metadata": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\"></div>"]}}], "execution_count": 0}, {"cell_type": "code", "source": ["config_dict = {\n    \"core\": dict(core),\n    \"logging\": dict(logging),\n    \"data_catalog\": dict(data_catalog)\n}"], "metadata": {"application/vnd.databricks.v1+cell": {"title": "", "showTitle": false, "inputWidgets": {}, "nuid": "c9dedb14-6ee0-475f-854d-facf6340e5dd"}}, "outputs": [{"output_type": "display_data", "metadata": {"application/vnd.databricks.v1+output": {"datasetInfos": [], "data": "<div class=\"ansiout\">Out[3]: {&#39;core&#39;: {&#39;random_seed&#39;: 0,\n  &#39;data_base_path&#39;: &#39;/dbfs/code_templates/regression/data&#39;,\n  &#39;reports_path&#39;: &#39;/dbfs/FileStore/Reports&#39;},\n &#39;logging&#39;: {&#39;version&#39;: 1,\n  &#39;disable_existing_loggers&#39;: False,\n  &#39;formatters&#39;: {&#39;default&#39;: {&#39;format&#39;: &#39;%(asctime)s — %(name)s — %(levelname)s — %(funcName)s:%(lineno)d — %(message)s&#39;,\n    &#39;datefmt&#39;: &#39;%Y-%m-%d %H:%M:%S&#39;},\n   &#39;simple&#39;: {&#39;format&#39;: &#39;%(message)s&#39;}},\n  &#39;handlers&#39;: {&#39;console_handler&#39;: {&#39;class&#39;: &#39;logging.StreamHandler&#39;,\n    &#39;level&#39;: &#39;DEBUG&#39;,\n    &#39;formatter&#39;: &#39;simple&#39;}},\n  &#39;root&#39;: {&#39;level&#39;: &#39;WARN&#39;,\n   &#39;handlers&#39;: [&#39;console_handler&#39;],\n   &#39;propogate&#39;: True},\n  &#39;loggers&#39;: {&#39;ta_lib&#39;: {&#39;level&#39;: &#39;WARN&#39;,\n    &#39;handlers&#39;: [&#39;console_handler&#39;],\n    &#39;propogate&#39;: True}}},\n &#39;data_catalog&#39;: {&#39;datasets&#39;: {&#39;raw&#39;: {&#39;orders&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;csv&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/raw/sales/orders.csv&#39;,\n     &#39;driver_params&#39;: {}},\n    &#39;product&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;csv&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/raw/sales/prod_master.csv&#39;,\n     &#39;driver_params&#39;: {}}},\n   &#39;cleaned&#39;: {&#39;orders&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;parquet&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/cleaned/sales/orders.parquet&#39;,\n     &#39;driver_params&#39;: {}},\n    &#39;product&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;parquet&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/cleaned/sales/product.parquet&#39;,\n     &#39;driver_params&#39;: {}},\n    &#39;sales&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;parquet&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/cleaned/sales/sales.parquet&#39;,\n     &#39;driver_params&#39;: {}}},\n   &#39;processed&#39;: {&#39;sales&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;parquet&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/processed/sales/sales.parquet&#39;,\n     &#39;driver_params&#39;: {}}},\n   &#39;train&#39;: {&#39;sales&#39;: {&#39;features&#39;: {&#39;type&#39;: &#39;ds&#39;,\n      &#39;format&#39;: &#39;parquet&#39;,\n      &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/train/sales/features.parquet&#39;,\n      &#39;driver_params&#39;: {&#39;save&#39;: {&#39;index&#39;: False}}},\n     &#39;target&#39;: {&#39;type&#39;: &#39;ds&#39;,\n      &#39;format&#39;: &#39;parquet&#39;,\n      &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/train/sales/target.parquet&#39;,\n      &#39;driver_params&#39;: {&#39;save&#39;: {&#39;index&#39;: False}}}}},\n   &#39;test&#39;: {&#39;sales&#39;: {&#39;features&#39;: {&#39;type&#39;: &#39;ds&#39;,\n      &#39;format&#39;: &#39;parquet&#39;,\n      &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/test/sales/features.parquet&#39;,\n      &#39;driver_params&#39;: {&#39;save&#39;: {&#39;index&#39;: False}}},\n     &#39;target&#39;: {&#39;type&#39;: &#39;ds&#39;,\n      &#39;format&#39;: &#39;parquet&#39;,\n      &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/test/sales/target.parquet&#39;,\n      &#39;driver_params&#39;: {&#39;save&#39;: {&#39;index&#39;: False}}}}},\n   &#39;score&#39;: {&#39;sales&#39;: {&#39;output&#39;: {&#39;type&#39;: &#39;ds&#39;,\n      &#39;format&#39;: &#39;parquet&#39;,\n      &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/test/sales/scored_output.parquet&#39;,\n      &#39;driver_params&#39;: {&#39;save&#39;: {&#39;index&#39;: False}}}}}}}}</div>", "removedWidgets": [], "addedWidgets": {}, "metadata": {}, "type": "html", "arguments": {}}}, "data": {"text/html": ["<style scoped>\n  .ansiout {\n    display: block;\n    unicode-bidi: embed;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    word-break: break-all;\n    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n    font-size: 13px;\n    color: #555;\n    margin-left: 4px;\n    line-height: 19px;\n  }\n</style>\n<div class=\"ansiout\">Out[3]: {&#39;core&#39;: {&#39;random_seed&#39;: 0,\n  &#39;data_base_path&#39;: &#39;/dbfs/code_templates/regression/data&#39;,\n  &#39;reports_path&#39;: &#39;/dbfs/FileStore/Reports&#39;},\n &#39;logging&#39;: {&#39;version&#39;: 1,\n  &#39;disable_existing_loggers&#39;: False,\n  &#39;formatters&#39;: {&#39;default&#39;: {&#39;format&#39;: &#39;%(asctime)s — %(name)s — %(levelname)s — %(funcName)s:%(lineno)d — %(message)s&#39;,\n    &#39;datefmt&#39;: &#39;%Y-%m-%d %H:%M:%S&#39;},\n   &#39;simple&#39;: {&#39;format&#39;: &#39;%(message)s&#39;}},\n  &#39;handlers&#39;: {&#39;console_handler&#39;: {&#39;class&#39;: &#39;logging.StreamHandler&#39;,\n    &#39;level&#39;: &#39;DEBUG&#39;,\n    &#39;formatter&#39;: &#39;simple&#39;}},\n  &#39;root&#39;: {&#39;level&#39;: &#39;WARN&#39;,\n   &#39;handlers&#39;: [&#39;console_handler&#39;],\n   &#39;propogate&#39;: True},\n  &#39;loggers&#39;: {&#39;ta_lib&#39;: {&#39;level&#39;: &#39;WARN&#39;,\n    &#39;handlers&#39;: [&#39;console_handler&#39;],\n    &#39;propogate&#39;: True}}},\n &#39;data_catalog&#39;: {&#39;datasets&#39;: {&#39;raw&#39;: {&#39;orders&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;csv&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/raw/sales/orders.csv&#39;,\n     &#39;driver_params&#39;: {}},\n    &#39;product&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;csv&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/raw/sales/prod_master.csv&#39;,\n     &#39;driver_params&#39;: {}}},\n   &#39;cleaned&#39;: {&#39;orders&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;parquet&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/cleaned/sales/orders.parquet&#39;,\n     &#39;driver_params&#39;: {}},\n    &#39;product&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;parquet&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/cleaned/sales/product.parquet&#39;,\n     &#39;driver_params&#39;: {}},\n    &#39;sales&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;parquet&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/cleaned/sales/sales.parquet&#39;,\n     &#39;driver_params&#39;: {}}},\n   &#39;processed&#39;: {&#39;sales&#39;: {&#39;type&#39;: &#39;ds&#39;,\n     &#39;format&#39;: &#39;parquet&#39;,\n     &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/processed/sales/sales.parquet&#39;,\n     &#39;driver_params&#39;: {}}},\n   &#39;train&#39;: {&#39;sales&#39;: {&#39;features&#39;: {&#39;type&#39;: &#39;ds&#39;,\n      &#39;format&#39;: &#39;parquet&#39;,\n      &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/train/sales/features.parquet&#39;,\n      &#39;driver_params&#39;: {&#39;save&#39;: {&#39;index&#39;: False}}},\n     &#39;target&#39;: {&#39;type&#39;: &#39;ds&#39;,\n      &#39;format&#39;: &#39;parquet&#39;,\n      &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/train/sales/target.parquet&#39;,\n      &#39;driver_params&#39;: {&#39;save&#39;: {&#39;index&#39;: False}}}}},\n   &#39;test&#39;: {&#39;sales&#39;: {&#39;features&#39;: {&#39;type&#39;: &#39;ds&#39;,\n      &#39;format&#39;: &#39;parquet&#39;,\n      &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/test/sales/features.parquet&#39;,\n      &#39;driver_params&#39;: {&#39;save&#39;: {&#39;index&#39;: False}}},\n     &#39;target&#39;: {&#39;type&#39;: &#39;ds&#39;,\n      &#39;format&#39;: &#39;parquet&#39;,\n      &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/test/sales/target.parquet&#39;,\n      &#39;driver_params&#39;: {&#39;save&#39;: {&#39;index&#39;: False}}}}},\n   &#39;score&#39;: {&#39;sales&#39;: {&#39;output&#39;: {&#39;type&#39;: &#39;ds&#39;,\n      &#39;format&#39;: &#39;parquet&#39;,\n      &#39;uri&#39;: &#39;/dbfs/code_templates/regression/data/test/sales/scored_output.parquet&#39;,\n      &#39;driver_params&#39;: {&#39;save&#39;: {&#39;index&#39;: False}}}}}}}}</div>"]}}], "execution_count": 0}], "metadata": {"application/vnd.databricks.v1+notebook": {"notebookName": "conf", "dashboards": [], "notebookMetadata": {"pythonIndentUnit": 4}, "language": "python", "widgets": {}, "notebookOrigID": 2838980130821827}}, "nbformat": 4, "nbformat_minor": 0}