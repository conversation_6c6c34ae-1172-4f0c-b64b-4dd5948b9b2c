{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "a9c6c895-03de-4995-a7c3-64d56362f2eb", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .table-result-container {\n", "    max-height: 300px;\n", "    overflow: auto;\n", "  }\n", "  table, th, td {\n", "    border: 1px solid black;\n", "    border-collapse: collapse;\n", "  }\n", "  th, td {\n", "    padding: 5px;\n", "  }\n", "  th {\n", "    text-align: left;\n", "  }\n", "</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>database</th><th>tableName</th><th>isTemporary</th></tr></thead><tbody><tr><td>demo</td><td>boston</td><td>false</td></tr><tr><td>demo</td><td>cancer</td><td>false</td></tr><tr><td>demo</td><td>his_tvr</td><td>false</td></tr><tr><td>demo</td><td>titanic_raw</td><td>false</td></tr></tbody></table></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "aggData": [], "aggError": "", "aggOverflow": false, "aggSchema": [], "aggSeriesLimitReached": false, "aggType": "", "arguments": {}, "columnCustomDisplayInfos": {}, "data": [["demo", "boston", false], ["demo", "cancer", false], ["demo", "his_tvr", false], ["demo", "titanic_raw", false]], "datasetInfos": [], "dbfsResultPath": null, "isJsonSchema": true, "overflow": false, "plotOptions": {"customPlotOptions": {}, "displayType": "table", "pivotAggregation": null, "pivotColumns": [], "xColumns": [], "yColumns": []}, "removedWidgets": [], "schema": [{"metadata": "{}", "name": "database", "type": "\"string\""}, {"metadata": "{}", "name": "tableName", "type": "\"string\""}, {"metadata": "{}", "name": "isTemporary", "type": "\"boolean\""}], "type": "table"}}, "output_type": "display_data"}], "source": ["%sql\n", "show tables from demo;\n", "-- select * from demo.titanic_raw;\n", "-- select * from demo.his_tvr;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "863f83e2-70b6-49ec-a9fe-0b1d64789897", "showTitle": false, "title": ""}}, "source": ["### Model Eval"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "b88bb7db-897b-4da3-9271-7cef70209432", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\"></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\"></div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["import ta_lib"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "c8cdaafc-1a3d-4bfa-bccf-817f8f881397", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">to<PERSON><PERSON><PERSON> attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n", "  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\n", "Attempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">to<PERSON><PERSON><PERSON> attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\nAttempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n</div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["# Regression\n", "import pandas as pd\n", "from sklearn.datasets import load_boston\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression\n", "from tigerml.model_eval import RegressionReport\n", "\n", "boston = spark.sql('select * from demo.boston').to<PERSON><PERSON><PERSON>()\n", "y = boston['target']\n", "X = boston.drop([\"target\"], axis=1)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.33, random_state=42)\n", "\n", "reg = LinearRegression()\n", "reg.fit(X_train, y_train)\n", "yhat_train = reg.predict(X_train)\n", "yhat_test = reg.predict(X_test)\n", "\n", "# Option 1 - with model\n", "regOpt1 = RegressionReport(y_train=y_train, x_train=X_train, x_test=X_test, y_test=y_test,\n", "                           model=reg)\n", "regOpt1.get_report(include_shap=True)\n", "\n", "# Option 2 - without model\n", "regOpt2 = RegressionReport(y_train=y_train, x_train=X_train, x_test=X_test, y_test=y_test,\n", "                           yhat_train=yhat_train, yhat_test=yhat_test)\n", "\n", "# Download this file to local by running following command in local machine, make sure databricks CLI is configured using token\n", "# databricks fs cp dbfs:/FileStore/code-templates/output_reports/model_eval_reg.html <target_path_in_local_without_filename/.>\n", "regOpt2.get_report(file_path = \"/dbfs/FileStore/code-templates/output_reports/model_eval_reg\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "3b6ca890-d226-4ebc-a777-2454a4a03d6f", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">to<PERSON><PERSON><PERSON> attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n", "  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\n", "Attempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n", "\r", "  0%|          | 0/19 [00:00&lt;?, ?it/s]\r", " 89%|████████▉ | 17/19 [00:00&lt;00:00, 167.46it/s]\r", "                                                \r", "WARNING:param.Warning: Nesting Layouts within a HoloMap makes it difficult to access your data or control how it appears; we recommend calling .collate() on the HoloMap in order to follow the recommended nesting structure shown in the Composing Data user guide (https://goo.gl/2YS8LJ)\n", "\r", "  0%|          | 0/19 [00:00&lt;?, ?it/s]\r", " 42%|████▏     | 8/19 [00:00&lt;00:00, 76.01it/s]\r", " 89%|████████▉ | 17/19 [00:00&lt;00:00, 77.29it/s]\r", "                                               \r", "WARNING:param.Warning: Nesting Layouts within a HoloMap makes it difficult to access your data or control how it appears; we recommend calling .collate() on the HoloMap in order to follow the recommended nesting structure shown in the Composing Data user guide (https://goo.gl/2YS8LJ)\n", "\r", "  0%|          | 0/19 [00:00&lt;?, ?it/s]\r", " 42%|████▏     | 8/19 [00:00&lt;00:00, 76.59it/s]\r", " 84%|████████▍ | 16/19 [00:00&lt;00:00, 77.33it/s]\r", "                                               \r", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">to<PERSON><PERSON><PERSON> attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\nAttempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n\r  0%|          | 0/19 [00:00&lt;?, ?it/s]\r 89%|████████▉ | 17/19 [00:00&lt;00:00, 167.46it/s]\r                                                \rWARNING:param.Warning: Nesting Layouts within a HoloMap makes it difficult to access your data or control how it appears; we recommend calling .collate() on the HoloMap in order to follow the recommended nesting structure shown in the Composing Data user guide (https://goo.gl/2YS8LJ)\n\r  0%|          | 0/19 [00:00&lt;?, ?it/s]\r 42%|████▏     | 8/19 [00:00&lt;00:00, 76.01it/s]\r 89%|████████▉ | 17/19 [00:00&lt;00:00, 77.29it/s]\r                                               \rWARNING:param.Warning: Nesting Layouts within a HoloMap makes it difficult to access your data or control how it appears; we recommend calling .collate() on the HoloMap in order to follow the recommended nesting structure shown in the Composing Data user guide (https://goo.gl/2YS8LJ)\n\r  0%|          | 0/19 [00:00&lt;?, ?it/s]\r 42%|████▏     | 8/19 [00:00&lt;00:00, 76.59it/s]\r 84%|████████▍ | 16/19 [00:00&lt;00:00, 77.33it/s]\r                                               \r</div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["# Binary classification\n", "import pandas as pd\n", "from sklearn.datasets import load_breast_cancer\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LogisticRegression\n", "from tigerml.model_eval import ClassificationReport\n", "\n", "cancer = spark.sql('select * from demo.cancer').to<PERSON><PERSON><PERSON>()\n", "y = cancer['target']\n", "X = cancer.drop([\"target\"], axis=1)\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.33, random_state=42)\n", "\n", "cls = LogisticRegression(max_iter=10000)\n", "cls.fit(X_train, y_train)\n", "yhat_train = cls.predict(X_train)\n", "yhat_test = cls.predict(X_test)\n", "\n", "# Option 1 - with model\n", "clsOpt1 = ClassificationReport(y_train=y_train, x_train=X_train, x_test=X_test, y_test=y_test,\n", "                               model=cls)\n", "clsOpt1.get_report(include_shap=True)\n", "\n", "# Option 2 - without model\n", "clsOpt2 = ClassificationReport(y_train=y_train, x_train=X_train, x_test=X_test, y_test=y_test,\n", "                               yhat_train=yhat_train, yhat_test=yhat_test)\n", "\n", "# Download this file to local by running following command in local machine, make sure databricks CLI is configured using token\n", "# databricks fs cp dbfs:/FileStore/code-templates/output_reports/model_eval_cls.html <target_path_in_local_without_filename/.>\n", "clsOpt2.get_report(file_path = \"/dbfs/FileStore/code-templates/output_reports/model_eval_cls\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "c5aeabbd-4d67-44e8-a9c0-b6197181d08e", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>"]}, "metadata": {"application/vnd.databricks.v1+output": {"arguments": {}, "data": "", "errorSummary": "", "type": "ipynbError"}}, "output_type": "display_data"}], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "language": "python", "notebookMetadata": {"pythonIndentUnit": 2}, "notebookName": "03_model_eval", "notebookOrigID": 3802253373203159, "widgets": {}}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 1}