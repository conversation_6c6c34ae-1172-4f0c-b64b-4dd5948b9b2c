{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "a9c6c895-03de-4995-a7c3-64d56362f2eb", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .table-result-container {\n", "    max-height: 300px;\n", "    overflow: auto;\n", "  }\n", "  table, th, td {\n", "    border: 1px solid black;\n", "    border-collapse: collapse;\n", "  }\n", "  th, td {\n", "    padding: 5px;\n", "  }\n", "  th {\n", "    text-align: left;\n", "  }\n", "</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>database</th><th>tableName</th><th>isTemporary</th></tr></thead><tbody><tr><td>demo</td><td>boston</td><td>false</td></tr><tr><td>demo</td><td>cancer</td><td>false</td></tr><tr><td>demo</td><td>his_tvr</td><td>false</td></tr><tr><td>demo</td><td>titanic_raw</td><td>false</td></tr></tbody></table></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "aggData": [], "aggError": "", "aggOverflow": false, "aggSchema": [], "aggSeriesLimitReached": false, "aggType": "", "arguments": {}, "columnCustomDisplayInfos": {}, "data": [["demo", "boston", false], ["demo", "cancer", false], ["demo", "his_tvr", false], ["demo", "titanic_raw", false]], "datasetInfos": [], "dbfsResultPath": null, "isJsonSchema": true, "overflow": false, "plotOptions": {"customPlotOptions": {}, "displayType": "table", "pivotAggregation": null, "pivotColumns": [], "xColumns": [], "yColumns": []}, "removedWidgets": [], "schema": [{"metadata": "{}", "name": "database", "type": "\"string\""}, {"metadata": "{}", "name": "tableName", "type": "\"string\""}, {"metadata": "{}", "name": "isTemporary", "type": "\"boolean\""}], "type": "table"}}, "output_type": "display_data"}], "source": ["%sql\n", "show tables from demo;\n", "-- select * from demo.titanic_raw;\n", "-- select * from demo.his_tvr;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "863f83e2-70b6-49ec-a9fe-0b1d64789897", "showTitle": false, "title": ""}}, "source": ["### AutoML"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "899c8f14-273c-4738-97d0-5053fb028166", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\"></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\"></div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["# MLFlow functions\n", "import os\n", "import glob\n", "import json\n", "import yaml\n", "import mlflow\n", "import pandas as pd\n", "import mlflow.sklearn\n", "\n", "\n", "def get_latest_file(path):\n", "  list_of_files = glob.glob(path + \"*\") # * means all if need specific format then *.csv\n", "  latest_file = max(list_of_files, key=os.path.getctime)\n", "  return latest_file\n", "\n", "def init_mlflow(expt_name, artifact_location, tracking_uri=None):\n", "    \"\"\"\n", "    Function to initialize the MLFlow Client for the experiment\n", "    Parameters:\n", "        expt_name (string): name of the mlflow experiment\n", "        tracking_uri (string): mlflow tracking uri\n", "        artifact_location (string): path of mlflow artifacts\n", "    Returns:\n", "        mlflow client & expt_id\n", "    \"\"\"\n", "    client = mlflow.tracking.MlflowClient(tracking_uri=tracking_uri)\n", "    try:\n", "        expt = client.get_experiment_by_name(expt_name)\n", "        expt_id = expt.experiment_id\n", "    except Exception as e:\n", "        type(e)\n", "        expt_id = client.create_experiment(\n", "            expt_name, artifact_location=artifact_location\n", "        )\n", "\n", "    mlflow.set_tracking_uri(tracking_uri)\n", "    mlflow.set_experiment(expt_name)\n", "\n", "    return(client, expt_id)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "79105173-2c89-4f99-967d-654edb6ac756", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\"></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\"></div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["import ta_lib"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "c8cdaafc-1a3d-4bfa-bccf-817f8f881397", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">/databricks/python/lib/python3.7/site-packages/tpot/base.py:31: DeprecationWarning: the imp module is deprecated in favour of importlib; see the module&#39;s documentation for alternative uses\n", "  import imp\n", "/databricks/python/lib/python3.7/site-packages/tpot/builtins/__init__.py:36: UserWarning: Warning: optional dependency `torch` is not available. - skipping import of NN models.\n", "  warnings.warn(&#34;Warning: optional dependency `torch` is not available. - skipping import of NN models.&#34;)\n", "/databricks/spark/python/pyspark/sql/pandas/conversion.py:92: UserWarning: to<PERSON><PERSON><PERSON> attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n", "  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\n", "Attempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n", "  warnings.warn(msg)\n", "Version 0.11.5 of tpot is outdated. Version 0.11.7 was released Wednesday January 06, 2021.\n", "\r", "Optimization Progress:   0%|          | 0/1550 [00:00&lt;?, ?pipeline/s]\r", "Optimization Progress:   0%|          | 1/1550 [00:07&lt;3:12:53,  7.47s/pipeline]\r", "Optimization Progress:   1%|          | 9/1550 [00:10&lt;2:16:57,  5.33s/pipeline]\r", "Optimization Progress:   1%|          | 17/1550 [00:12&lt;1:37:23,  3.81s/pipeline]\r", "Optimization Progress:   2%|▏         | 25/1550 [00:13&lt;1:09:06,  2.72s/pipeline]\r", "Optimization Progress:   2%|▏         | 33/1550 [00:16&lt;51:02,  2.02s/pipeline]  \r", "Optimization Progress:   3%|▎         | 41/1550 [00:18&lt;36:48,  1.46s/pipeline]\r", "Optimization Progress:   3%|▎         | 49/1550 [00:18&lt;26:27,  1.06s/pipeline]\r", "Optimization Progress:   3%|▎         | 50/1550 [00:19&lt;20:58,  1.19pipeline/s]\r", "Optimization Progress:   3%|▎         | 52/1550 [00:24&lt;35:13,  1.41s/pipeline]\r", "Optimization Progress:   3%|▎         | 53/1550 [00:28&lt;53:39,  2.15s/pipeline]\r", "Optimization Progress:   4%|▍         | 61/1550 [00:30&lt;38:50,  1.57s/pipeline]\r", "Optimization Progress:   4%|▍         | 69/1550 [00:32&lt;28:41,  1.16s/pipeline]\r", "Optimization Progress:   5%|▍         | 77/1550 [00:36&lt;23:48,  1.03pipeline/s]\r", "Optimization Progress:   5%|▌         | 85/1550 [00:39&lt;19:39,  1.24pipeline/s]\r", "Optimization Progress:   6%|▌         | 93/1550 [00:42&lt;16:44,  1.45pipeline/s]\n", "Generation 1 - Current best internal CV score: 0.752601110009221\r", "Optimization Progress:   7%|▋         | 101/1550 [00:44&lt;13:11,  1.83pipeline/s]\r", "Optimization Progress:   7%|▋         | 104/1550 [00:51&lt;26:04,  1.08s/pipeline]\r", "Optimization Progress:   7%|▋         | 105/1550 [00:54&lt;41:55,  1.74s/pipeline]\r", "Optimization Progress:   7%|▋         | 113/1550 [00:58&lt;32:42,  1.37s/pipeline]\r", "Optimization Progress:   8%|▊         | 121/1550 [01:04&lt;27:36,  1.16s/pipeline]\r", "Optimization Progress:   8%|▊         | 129/1550 [01:05&lt;20:38,  1.15pipeline/s]\r", "Optimization Progress:   9%|▉         | 137/1550 [01:09&lt;17:28,  1.35pipeline/s]\r", "Optimization Progress:   9%|▉         | 145/1550 [01:22&lt;23:45,  1.01s/pipeline]\n", "Generation 2 - Current best internal CV score: 0.7526907545626946\r", "Optimization Progress:  10%|▉         | 151/1550 [01:23&lt;17:44,  1.31pipeline/s]\r", "Optimization Progress:  10%|▉         | 152/1550 [01:32&lt;1:15:53,  3.26s/pipeline]\r", "Optimization Progress:  10%|█         | 160/1550 [01:34&lt;54:37,  2.36s/pipeline]  \r", "Optimization Progress:  11%|█         | 168/1550 [01:37&lt;40:15,  1.75s/pipeline]\r", "Optimization Progress:  11%|█▏        | 176/1550 [01:41&lt;31:33,  1.38s/pipeline]\r", "Optimization Progress:  12%|█▏        | 184/1550 [01:45&lt;25:48,  1.13s/pipeline]\r", "Optimization Progress:  12%|█▏        | 192/1550 [01:48&lt;20:28,  1.11pipeline/s]\r", "Optimization Progress:  13%|█▎        | 200/1550 [01:49&lt;15:03,  1.49pipeline/s]\n", "Generation 3 - Current best internal CV score: 0.7526907545626946\r", "Optimization Progress:  13%|█▎        | 201/1550 [01:52&lt;29:13,  1.30s/pipeline]\r", "Optimization Progress:  13%|█▎        | 202/1550 [01:58&lt;59:15,  2.64s/pipeline]\r", "Optimization Progress:  13%|█▎        | 203/1550 [02:06&lt;1:34:07,  4.19s/pipeline]\r", "Optimization Progress:  14%|█▎        | 211/1550 [02:09&lt;1:08:19,  3.06s/pipeline]\r", "Optimization Progress:  14%|█▍        | 219/1550 [02:20&lt;56:57,  2.57s/pipeline]  \r", "Optimization Progress:  15%|█▍        | 227/1550 [02:24&lt;42:56,  1.95s/pipeline]\r", "Optimization Progress:  15%|█▌        | 235/1550 [02:29&lt;33:18,  1.52s/pipeline]\r", "Optimization Progress:  16%|█▌        | 243/1550 [02:32&lt;25:37,  1.18s/pipeline]\r", "Optimization Progress:  16%|█▌        | 251/1550 [02:35&lt;20:48,  1.04pipeline/s]\n", "Generation 4 - Current best internal CV score: 0.7550758042347373\r", "Optimization Progress:  16%|█▋        | 252/1550 [02:43&lt;1:07:44,  3.13s/pipeline]\r", "Optimization Progress:  16%|█▋        | 253/1550 [02:46&lt;1:05:36,  3.04s/pipeline]\r", "Optimization Progress:  17%|█▋        | 261/1550 [02:50&lt;48:59,  2.28s/pipeline]  \r", "Optimization Progress:  17%|█▋        | 269/1550 [02:57&lt;38:56,  1.82s/pipeline]\r", "Optimization Progress:  18%|█▊        | 277/1550 [03:00&lt;29:51,  1.41s/pipeline]\r", "Optimization Progress:  18%|█▊        | 285/1550 [03:04&lt;23:41,  1.12s/pipeline]\r", "Optimization Progress:  19%|█▉        | 293/1550 [03:07&lt;18:53,  1.11pipeline/s]\n", "Generation 5 - Current best internal CV score: 0.7550758042347373\r", "Optimization Progress:  19%|█▉        | 301/1550 [03:07&lt;13:16,  1.57pipeline/s]\r", "Optimization Progress:  20%|█▉        | 303/1550 [03:15&lt;35:35,  1.71s/pipeline]\r", "Optimization Progress:  20%|█▉        | 305/1550 [03:18&lt;34:35,  1.67s/pipeline]\r", "Optimization Progress:  20%|██        | 312/1550 [03:21&lt;26:19,  1.28s/pipeline]\r", "Optimization Progress:  21%|██        | 320/1550 [03:26&lt;22:17,  1.09s/pipeline]\r", "Optimization Progress:  21%|██        | 328/1550 [03:30&lt;18:04,  1.13pipeline/s]\r", "Optimization Progress:  22%|██▏       | 336/1550 [03:34&lt;15:39,  1.29pipeline/s]\r", "Optimization Progress:  22%|██▏       | 344/1550 [03:36&lt;13:01,  1.54pipeline/s]\n", "Generation 6 - Current best internal CV score: 0.7550758042347373\r", "Optimization Progress:  23%|██▎       | 351/1550 [03:38&lt;10:25,  1.92pipeline/s]\r", "Optimization Progress:  23%|██▎       | 352/1550 [03:47&lt;1:01:19,  3.07s/pipeline]\r", "Optimization Progress:  23%|██▎       | 353/1550 [03:52&lt;1:11:47,  3.60s/pipeline]\r", "Optimization Progress:  23%|██▎       | 361/1550 [03:56&lt;53:11,  2.68s/pipeline]  \r", "Optimization Progress:  24%|██▍       | 369/1550 [04:01&lt;40:21,  2.05s/pipeline]\r", "Optimization Progress:  24%|██▍       | 377/1550 [04:06&lt;31:33,  1.61s/pipeline]\r", "Optimization Progress:  25%|██▍       | 385/1550 [04:15&lt;29:00,  1.49s/pipeline]\r", "Optimization Progress:  25%|██▌       | 393/1550 [04:21&lt;23:55,  1.24s/pipeline]\r", "Optimization Progress:  26%|██▌       | 401/1550 [04:22&lt;17:45,  1.08pipeline/s]\n", "Generation 7 - Current best internal CV score: 0.7550758042347373\r", "Optimization Progress:  26%|██▌       | 402/1550 [04:32&lt;1:09:07,  3.61s/pipeline]\r", "Optimization Progress:  26%|██▌       | 404/1550 [04:37&lt;1:01:48,  3.24s/pipeline]\r", "Optimization Progress:  27%|██▋       | 412/1550 [04:42&lt;46:42,  2.46s/pipeline]  \r", "Optimization Progress:  27%|██▋       | 420/1550 [04:46&lt;35:12,  1.87s/pipeline]\r", "Optimization Progress:  28%|██▊       | 428/1550 [04:52&lt;28:59,  1.55s/pipeline]\r", "Optimization Progress:  28%|██▊       | 436/1550 [04:55&lt;22:19,  1.20s/pipeline]\r", "Optimization Progress:  29%|██▊       | 444/1550 [04:59&lt;18:10,  1.01pipeline/s]\n", "Generation 8 - Current best internal CV score: 0.7550758042347373\r", "Optimization Progress:  29%|██▉       | 451/1550 [05:00&lt;13:03,  1.40pipeline/s]\r", "Optimization Progress:  29%|██▉       | 452/1550 [05:11&lt;1:10:06,  3.83s/pipeline]\r", "Optimization Progress:  29%|██▉       | 453/1550 [05:22&lt;1:47:27,  5.88s/pipeline]\r", "Optimization Progress:  30%|██▉       | 461/1550 [05:25&lt;1:16:52,  4.24s/pipeline]\r", "Optimization Progress:  30%|███       | 469/1550 [05:36&lt;1:00:52,  3.38s/pipeline]\r", "Optimization Progress:  31%|███       | 477/1550 [05:46&lt;49:15,  2.75s/pipeline]  \r", "Optimization Progress:  31%|███▏      | 485/1550 [05:53&lt;38:24,  2.16s/pipeline]\r", "Optimization Progress:  32%|███▏      | 493/1550 [05:56&lt;28:54,  1.64s/pipeline]\r", "Optimization Progress:  32%|███▏      | 501/1550 [05:57&lt;20:53,  1.19s/pipeline]\n", "Generation 9 - Current best internal CV score: 0.7550758042347373\r", "                                                                               \r\n", "\r", "Optimization Progress:  32%|███▏      | 501/1550 [05:57&lt;20:53,  1.19s/pipeline]\r", "                                                                               \r", "The optimized pipeline was not improved after evaluating 5 more generations. Will end the optimization process.\n", "\n", "TPOT closed prematurely. Will use the current best pipeline.\n", "\r", "Optimization Progress:  32%|███▏      | 501/1550 [05:57&lt;20:53,  1.19s/pipeline]\r", "                                                                               \r\n", "Best pipeline: RandomForestClassifier(input_matrix, bootstrap=True, criterion=entropy, max_features=0.6000000000000001, min_samples_leaf=10, min_samples_split=5, n_estimators=100)\n", "/databricks/python/lib/python3.7/site-packages/sklearn/utils/validation.py:72: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().\n", "  return f(**kwargs)\n", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">/databricks/python/lib/python3.7/site-packages/tpot/base.py:31: DeprecationWarning: the imp module is deprecated in favour of importlib; see the module&#39;s documentation for alternative uses\n  import imp\n/databricks/python/lib/python3.7/site-packages/tpot/builtins/__init__.py:36: UserWarning: Warning: optional dependency `torch` is not available. - skipping import of NN models.\n  warnings.warn(&#34;Warning: optional dependency `torch` is not available. - skipping import of NN models.&#34;)\n/databricks/spark/python/pyspark/sql/pandas/conversion.py:92: UserWarning: to<PERSON><PERSON><PERSON> attempted Arrow optimization because &#39;spark.sql.execution.arrow.pyspark.enabled&#39; is set to true; however, failed by the reason below:\n  PyArrow &gt;= 1.0.0 must be installed; however, your version was 0.15.1.\nAttempting non-optimization as &#39;spark.sql.execution.arrow.pyspark.fallback.enabled&#39; is set to true.\n  warnings.warn(msg)\nVersion 0.11.5 of tpot is outdated. Version 0.11.7 was released Wednesday January 06, 2021.\n\rOptimization Progress:   0%|          | 0/1550 [00:00&lt;?, ?pipeline/s]\rOptimization Progress:   0%|          | 1/1550 [00:07&lt;3:12:53,  7.47s/pipeline]\rOptimization Progress:   1%|          | 9/1550 [00:10&lt;2:16:57,  5.33s/pipeline]\rOptimization Progress:   1%|          | 17/1550 [00:12&lt;1:37:23,  3.81s/pipeline]\rOptimization Progress:   2%|▏         | 25/1550 [00:13&lt;1:09:06,  2.72s/pipeline]\rOptimization Progress:   2%|▏         | 33/1550 [00:16&lt;51:02,  2.02s/pipeline]  \rOptimization Progress:   3%|▎         | 41/1550 [00:18&lt;36:48,  1.46s/pipeline]\rOptimization Progress:   3%|▎         | 49/1550 [00:18&lt;26:27,  1.06s/pipeline]\rOptimization Progress:   3%|▎         | 50/1550 [00:19&lt;20:58,  1.19pipeline/s]\rOptimization Progress:   3%|▎         | 52/1550 [00:24&lt;35:13,  1.41s/pipeline]\rOptimization Progress:   3%|▎         | 53/1550 [00:28&lt;53:39,  2.15s/pipeline]\rOptimization Progress:   4%|▍         | 61/1550 [00:30&lt;38:50,  1.57s/pipeline]\rOptimization Progress:   4%|▍         | 69/1550 [00:32&lt;28:41,  1.16s/pipeline]\rOptimization Progress:   5%|▍         | 77/1550 [00:36&lt;23:48,  1.03pipeline/s]\rOptimization Progress:   5%|▌         | 85/1550 [00:39&lt;19:39,  1.24pipeline/s]\rOptimization Progress:   6%|▌         | 93/1550 [00:42&lt;16:44,  1.45pipeline/s]\nGeneration 1 - Current best internal CV score: 0.752601110009221\rOptimization Progress:   7%|▋         | 101/1550 [00:44&lt;13:11,  1.83pipeline/s]\rOptimization Progress:   7%|▋         | 104/1550 [00:51&lt;26:04,  1.08s/pipeline]\rOptimization Progress:   7%|▋         | 105/1550 [00:54&lt;41:55,  1.74s/pipeline]\rOptimization Progress:   7%|▋         | 113/1550 [00:58&lt;32:42,  1.37s/pipeline]\rOptimization Progress:   8%|▊         | 121/1550 [01:04&lt;27:36,  1.16s/pipeline]\rOptimization Progress:   8%|▊         | 129/1550 [01:05&lt;20:38,  1.15pipeline/s]\rOptimization Progress:   9%|▉         | 137/1550 [01:09&lt;17:28,  1.35pipeline/s]\rOptimization Progress:   9%|▉         | 145/1550 [01:22&lt;23:45,  1.01s/pipeline]\nGeneration 2 - Current best internal CV score: 0.7526907545626946\rOptimization Progress:  10%|▉         | 151/1550 [01:23&lt;17:44,  1.31pipeline/s]\rOptimization Progress:  10%|▉         | 152/1550 [01:32&lt;1:15:53,  3.26s/pipeline]\rOptimization Progress:  10%|█         | 160/1550 [01:34&lt;54:37,  2.36s/pipeline]  \rOptimization Progress:  11%|█         | 168/1550 [01:37&lt;40:15,  1.75s/pipeline]\rOptimization Progress:  11%|█▏        | 176/1550 [01:41&lt;31:33,  1.38s/pipeline]\rOptimization Progress:  12%|█▏        | 184/1550 [01:45&lt;25:48,  1.13s/pipeline]\rOptimization Progress:  12%|█▏        | 192/1550 [01:48&lt;20:28,  1.11pipeline/s]\rOptimization Progress:  13%|█▎        | 200/1550 [01:49&lt;15:03,  1.49pipeline/s]\nGeneration 3 - Current best internal CV score: 0.7526907545626946\rOptimization Progress:  13%|█▎        | 201/1550 [01:52&lt;29:13,  1.30s/pipeline]\rOptimization Progress:  13%|█▎        | 202/1550 [01:58&lt;59:15,  2.64s/pipeline]\rOptimization Progress:  13%|█▎        | 203/1550 [02:06&lt;1:34:07,  4.19s/pipeline]\rOptimization Progress:  14%|█▎        | 211/1550 [02:09&lt;1:08:19,  3.06s/pipeline]\rOptimization Progress:  14%|█▍        | 219/1550 [02:20&lt;56:57,  2.57s/pipeline]  \rOptimization Progress:  15%|█▍        | 227/1550 [02:24&lt;42:56,  1.95s/pipeline]\rOptimization Progress:  15%|█▌        | 235/1550 [02:29&lt;33:18,  1.52s/pipeline]\rOptimization Progress:  16%|█▌        | 243/1550 [02:32&lt;25:37,  1.18s/pipeline]\rOptimization Progress:  16%|█▌        | 251/1550 [02:35&lt;20:48,  1.04pipeline/s]\nGeneration 4 - Current best internal CV score: 0.7550758042347373\rOptimization Progress:  16%|█▋        | 252/1550 [02:43&lt;1:07:44,  3.13s/pipeline]\rOptimization Progress:  16%|█▋        | 253/1550 [02:46&lt;1:05:36,  3.04s/pipeline]\rOptimization Progress:  17%|█▋        | 261/1550 [02:50&lt;48:59,  2.28s/pipeline]  \rOptimization Progress:  17%|█▋        | 269/1550 [02:57&lt;38:56,  1.82s/pipeline]\rOptimization Progress:  18%|█▊        | 277/1550 [03:00&lt;29:51,  1.41s/pipeline]\rOptimization Progress:  18%|█▊        | 285/1550 [03:04&lt;23:41,  1.12s/pipeline]\rOptimization Progress:  19%|█▉        | 293/1550 [03:07&lt;18:53,  1.11pipeline/s]\nGeneration 5 - Current best internal CV score: 0.7550758042347373\rOptimization Progress:  19%|█▉        | 301/1550 [03:07&lt;13:16,  1.57pipeline/s]\rOptimization Progress:  20%|█▉        | 303/1550 [03:15&lt;35:35,  1.71s/pipeline]\rOptimization Progress:  20%|█▉        | 305/1550 [03:18&lt;34:35,  1.67s/pipeline]\rOptimization Progress:  20%|██        | 312/1550 [03:21&lt;26:19,  1.28s/pipeline]\rOptimization Progress:  21%|██        | 320/1550 [03:26&lt;22:17,  1.09s/pipeline]\rOptimization Progress:  21%|██        | 328/1550 [03:30&lt;18:04,  1.13pipeline/s]\rOptimization Progress:  22%|██▏       | 336/1550 [03:34&lt;15:39,  1.29pipeline/s]\rOptimization Progress:  22%|██▏       | 344/1550 [03:36&lt;13:01,  1.54pipeline/s]\nGeneration 6 - Current best internal CV score: 0.7550758042347373\rOptimization Progress:  23%|██▎       | 351/1550 [03:38&lt;10:25,  1.92pipeline/s]\rOptimization Progress:  23%|██▎       | 352/1550 [03:47&lt;1:01:19,  3.07s/pipeline]\rOptimization Progress:  23%|██▎       | 353/1550 [03:52&lt;1:11:47,  3.60s/pipeline]\rOptimization Progress:  23%|██▎       | 361/1550 [03:56&lt;53:11,  2.68s/pipeline]  \rOptimization Progress:  24%|██▍       | 369/1550 [04:01&lt;40:21,  2.05s/pipeline]\rOptimization Progress:  24%|██▍       | 377/1550 [04:06&lt;31:33,  1.61s/pipeline]\rOptimization Progress:  25%|██▍       | 385/1550 [04:15&lt;29:00,  1.49s/pipeline]\rOptimization Progress:  25%|██▌       | 393/1550 [04:21&lt;23:55,  1.24s/pipeline]\rOptimization Progress:  26%|██▌       | 401/1550 [04:22&lt;17:45,  1.08pipeline/s]\nGeneration 7 - Current best internal CV score: 0.7550758042347373\rOptimization Progress:  26%|██▌       | 402/1550 [04:32&lt;1:09:07,  3.61s/pipeline]\rOptimization Progress:  26%|██▌       | 404/1550 [04:37&lt;1:01:48,  3.24s/pipeline]\rOptimization Progress:  27%|██▋       | 412/1550 [04:42&lt;46:42,  2.46s/pipeline]  \rOptimization Progress:  27%|██▋       | 420/1550 [04:46&lt;35:12,  1.87s/pipeline]\rOptimization Progress:  28%|██▊       | 428/1550 [04:52&lt;28:59,  1.55s/pipeline]\rOptimization Progress:  28%|██▊       | 436/1550 [04:55&lt;22:19,  1.20s/pipeline]\rOptimization Progress:  29%|██▊       | 444/1550 [04:59&lt;18:10,  1.01pipeline/s]\nGeneration 8 - Current best internal CV score: 0.7550758042347373\rOptimization Progress:  29%|██▉       | 451/1550 [05:00&lt;13:03,  1.40pipeline/s]\rOptimization Progress:  29%|██▉       | 452/1550 [05:11&lt;1:10:06,  3.83s/pipeline]\rOptimization Progress:  29%|██▉       | 453/1550 [05:22&lt;1:47:27,  5.88s/pipeline]\rOptimization Progress:  30%|██▉       | 461/1550 [05:25&lt;1:16:52,  4.24s/pipeline]\rOptimization Progress:  30%|███       | 469/1550 [05:36&lt;1:00:52,  3.38s/pipeline]\rOptimization Progress:  31%|███       | 477/1550 [05:46&lt;49:15,  2.75s/pipeline]  \rOptimization Progress:  31%|███▏      | 485/1550 [05:53&lt;38:24,  2.16s/pipeline]\rOptimization Progress:  32%|███▏      | 493/1550 [05:56&lt;28:54,  1.64s/pipeline]\rOptimization Progress:  32%|███▏      | 501/1550 [05:57&lt;20:53,  1.19s/pipeline]\nGeneration 9 - Current best internal CV score: 0.7550758042347373\r                                                                               \r\n\rOptimization Progress:  32%|███▏      | 501/1550 [05:57&lt;20:53,  1.19s/pipeline]\r                                                                               \rThe optimized pipeline was not improved after evaluating 5 more generations. Will end the optimization process.\n\nTPOT closed prematurely. Will use the current best pipeline.\n\rOptimization Progress:  32%|███▏      | 501/1550 [05:57&lt;20:53,  1.19s/pipeline]\r                                                                               \r\nBest pipeline: RandomForestClassifier(input_matrix, bootstrap=True, criterion=entropy, max_features=0.6000000000000001, min_samples_leaf=10, min_samples_split=5, n_estimators=100)\n/databricks/python/lib/python3.7/site-packages/sklearn/utils/validation.py:72: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().\n  return f(**kwargs)\n</div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["# AutoML\n", "import numpy as np\n", "import pandas as pd\n", "from tigerml.automl import *\n", "\n", "# Reading data\n", "c_data = spark.sql('select * from demo.titanic_raw').toPandas()\n", "c_data['Cabin'] = c_data['Cabin'].fillna(value=np.nan)\n", "c_data['Embarked'] = c_data['Embarked'].fillna(value=np.nan)\n", "\n", "# AutoML Config\n", "config = {\n", "  \"table_name\": \"demo.titanic_raw\",\n", "  \"model_type\": \"Classification\",\n", "  \"generations\": 30,\n", "  \"population_size\": 50,\n", "  \"name\": \"titanic\",\n", "  \"task\": TASKS.classification,\n", "  \"drop_cols\": ['PassengerId', 'Name', 'Ticket'],\n", "  \"target\": \"Survived\",\n", "  \"train_size\": 0.75,\n", "  \"remove_na\": False\n", "}\n", "\n", "# AutoML Run\n", "classifier = AutoML(\n", " generations=config[\"generations\"],\n", " population_size=config[\"population_size\"],\n", " name=config[\"name\"],\n", " task=config[\"task\"], #use TASKS.regression for regression.\n", ")\n", "\n", "# type(classifier)\n", "classifier.prep_data(\n", " data=c_data.drop(config[\"drop_cols\"], axis=1),\n", " dv_name=config[\"target\"],\n", " train_size=config[\"train_size\"],\n", " remove_na=config[\"remove_na\"]\n", ")\n", "classifier.fit()\n", "\n", "# Download this file to local by running following command in local machine, make sure databricks CLI is configured using token\n", "# databricks fs cp dbfs:/FileStore/code-templates/output_reports/<file_name> <target_path_in_local_without_filename/.>\n", "# Example Filename - dbfs:/FileStore/code-templates/output_reports/titanic_structured_classification_at_2021-04-28_15-31-19.html\n", "reports_path = \"/dbfs/FileStore/code-templates/output_reports/\"\n", "mertrics = classifier.get_report(no_of_pipelines=5, format='.html', reports_path = reports_path)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "c9557926-cd0c-4729-bebe-d81fbd41a058", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\"></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\"></div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["# MLFlow experiment setup\n", "expt_name = \"/Users/<USER>/Code-Templates/AutoML Classifcation\"\n", "artifact_location = \"dbfs:/FileStore/code-templates/mlflow_artifacts/automl/classification\"\n", "client, expt_id = init_mlflow(expt_name, artifact_location)\n", "report_file = get_latest_file(reports_path)\n", "\n", "# Register parameters\n", "for key, val in config.items():\n", "  mlflow.log_param(key, val)\n", "\n", "# Register Report as artifact\n", "mlflow.log_artifact(report_file, \"Report_path\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "8b47893f-d2c0-4b36-a12b-3f1781659a06", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>\n", "<div class=\"ansiout\">Package                       Version             Location\n", "----------------------------- ------------------- --------------------------------------\n", "absl-py                       0.10.0\n", "alabaster                     0.7.12\n", "alembic                       1.4.3\n", "altair                        4.1.0\n", "appdirs                       1.4.4\n", "argon2-cffi                   20.1.0\n", "arviz                         0.10.0\n", "asn1crypto                    1.4.0\n", "astunparse                    1.6.3\n", "attrs                         20.1.0\n", "azure-core                    1.8.2\n", "azure-storage-blob            12.5.0\n", "Babel                         2.8.0\n", "backcall                      0.2.0\n", "bandit                        1.6.2\n", "black                         19.10b0\n", "bleach                        3.2.1\n", "blinker                       1.4\n", "bokeh                         2.2.3\n", "brotlipy                      0.7.0\n", "cachetools                    4.2.2\n", "category-encoders             2.2.2\n", "certifi                       2020.6.20\n", "cffi                          1.14.0\n", "cftime                        1.2.1\n", "chardet                       3.0.4\n", "click                         7.1.2\n", "cloudpickle                   1.3.0\n", "colorcet                      2.0.2\n", "configparser                  5.0.1\n", "cryptography                  3.1.1\n", "cycler                        0.10.0\n", "cytoolz                       0.11.0\n", "dask                          2.30.0\n", "databricks-cli                0.9.1\n", "datashader                    0.11.1\n", "datashape                     0.5.4\n", "deap                          1.3.1\n", "decorator                     4.4.2\n", "defusedxml                    0.6.0\n", "distributed                   2.30.0\n", "docker                        4.3.1\n", "docker-pycreds                0.4.0\n", "docutils                      0.16\n", "dparse                        0.5.1\n", "eli5                          0.10.1\n", "entrypoints                   0.3\n", "fastprogress                  1.0.0\n", "flake8                        3.8.3\n", "flake8-bandit                 2.1.2\n", "flake8-black                  0.2.1\n", "flake8-docstrings             1.5.0\n", "flake8-isort                  3.0.1\n", "flake8-polyfill               1.0.2\n", "Flask                         1.1.2\n", "fsspec                        0.7.4\n", "future                        0.16.0\n", "gast                          0.3.2\n", "gitdb                         4.0.5\n", "GitPython                     3.1.3\n", "google-auth                   1.22.1\n", "google-auth-oauthlib          0.4.1\n", "google-pasta                  0.2.0\n", "gorilla                       0.3.0\n", "graphviz                      0.14\n", "great-expectations            0.13.4\n", "grpcio                        1.32.0\n", "gunicorn                      20.0.4\n", "h5py                          2.10.0\n", "HeapDict                      1.0.1\n", "holoviews                     1.13.4\n", "hvplot                        0.6.0\n", "hypothesis                    5.20.3\n", "idna                          2.10\n", "imageio                       2.9.0\n", "imagesize                     1.2.0\n", "importlib-metadata            2.0.0\n", "iniconfig                     1.0.1\n", "invoke                        1.3.0\n", "ipykernel                     5.3.4\n", "ipython                       7.18.1\n", "ipython-genutils              0.2.0\n", "ipywidgets                    7.5.1\n", "isodate                       0.6.0\n", "isort                         4.3.21\n", "itsdangerous                  1.1.0\n", "jedi                          0.17.2\n", "Jinja2                        2.11.2\n", "joblib                        0.17.0\n", "json5                         0.9.5\n", "jsonpatch                     1.32\n", "jsonpointer                   2.1\n", "jsonschema                    3.2.0\n", "jupyter-client                6.1.7\n", "jupyter-core                  4.6.3\n", "jupyter-sphinx                0.3.1\n", "jupyterlab                    2.2.6\n", "jupyterlab-server             1.2.0\n", "Keras-Preprocessing           1.1.2\n", "kiwisolver                    1.2.0\n", "llvmlite                      0.34.0\n", "locket                        0.2.0\n", "luminol                       0.4\n", "lxml                          4.6.1\n", "Mako                          1.1.3\n", "Markdown                      3.3.2\n", "MarkupSafe                    1.1.1\n", "matplotlib                    3.3.2\n", "mccabe                        0.6.1\n", "mistune                       0.8.4\n", "mkl-fft                       1.2.0\n", "mkl-random                    1.1.1\n", "mkl-service                   2.3.0\n", "mlxtend                       0.17.3\n", "more-itertools                8.5.0\n", "msgpack                       1.0.0\n", "msrest                        0.6.19\n", "multipledispatch              0.6.0\n", "mypy-extensions               0.4.3\n", "natsort                       7.0.1\n", "nbconvert                     5.6.1\n", "nbformat                      5.0.7\n", "nbsphinx                      0.7.1\n", "netCDF4                       1.5.3\n", "networkx                      2.5\n", "nose                          1.3.7\n", "notebook                      6.1.4\n", "numba                         0.51.2\n", "numpy                         1.19.1\n", "oauthlib                      3.1.0\n", "olefile                       0.46\n", "opt-einsum                    3.3.0\n", "packaging                     20.4\n", "pandas                        1.0.5\n", "pandas-flavor                 0.2.0\n", "pandocfilters                 1.4.2\n", "panel                         0.9.7\n", "param                         1.9.3\n", "parso                         0.7.0\n", "partd                         1.1.0\n", "pathspec                      0.7.0\n", "patsy                         0.5.1\n", "pbr                           5.5.0\n", "pexpect                       4.8.0\n", "pickleshare                   0.7.5\n", "Pillow                        8.0.0\n", "pip                           20.2.2\n", "pluggy                        0.13.1\n", "ply                           3.11\n", "pockets                       0.9.1\n", "prometheus-client             0.8.0\n", "prometheus-flask-exporter     0.18.1\n", "prompt-toolkit                3.0.8\n", "protobuf                      3.13.0\n", "psutil                        5.7.2\n", "ptyprocess                    0.6.0\n", "py                            1.9.0\n", "pyarrow                       0.15.1\n", "pyasn1                        0.4.8\n", "pyasn1-modules                0.2.8\n", "pycodestyle                   2.6.0\n", "pycparser                     2.20\n", "pyct                          0.4.8\n", "pydocstyle                    5.1.1\n", "pyflakes                      2.2.0\n", "Pygments                      2.7.1\n", "pygpu                         0.7.6\n", "pyjanitor                     0.20.9\n", "PyJWT                         1.7.1\n", "pymc3                         3.9.3\n", "Pyomo                         5.7\n", "pyOpenSSL                     19.1.0\n", "pyparsing                     2.4.7\n", "pyrsistent                    0.17.3\n", "PySocks                       1.7.1\n", "pytest                        6.0.1\n", "python-dateutil               2.8.1\n", "python-editor                 1.0.4\n", "python-slugify                3.0.4\n", "pytz                          2020.1\n", "PyUtilib                      6.0.0\n", "pyviz-comms                   0.7.6\n", "PyWavelets                    1.1.1\n", "PyYAML                        5.3.1\n", "pyzmq                         19.0.2\n", "querystring-parser            1.2.4\n", "regex                         2020.7.14\n", "requests                      2.23.0\n", "requests-oauthlib             1.3.0\n", "rsa                           4.6\n", "ruamel.yaml                   0.17.2\n", "ruamel.yaml.clib              0.2.2\n", "ruptures                      1.1.3\n", "safety                        1.9.0\n", "scikit-image                  0.17.2\n", "scikit-learn                  0.23.2\n", "scipy                         1.5.2\n", "seaborn                       0.10.1\n", "Send2Trash                    1.5.0\n", "setuptools                    50.3.0.post20201006\n", "shap                          0.35.0\n", "singledispatch                *******\n", "six                           1.15.0\n", "smmap                         3.0.4\n", "snowballstemmer               2.0.0\n", "sortedcontainers              2.2.2\n", "Sphinx                        3.2.1\n", "sphinx-rtd-theme              0.5.0\n", "sphinxcontrib-applehelp       1.0.2\n", "sphinxcontrib-devhelp         1.0.2\n", "sphinxcontrib-htmlhelp        1.0.3\n", "sphinxcontrib-jsmath          1.0.1\n", "sphinxcontrib-napoleon        0.7\n", "sphinxcontrib-qthelp          1.0.3\n", "sphinxcontrib-serializinghtml 1.1.4\n", "SQLAlchemy                    1.3.13\n", "sqlparse                      0.4.1\n", "statsmodels                   0.11.1\n", "ste<PERSON>ore                     3.2.2\n", "stopit                        1.1.2\n", "ta-lib                        1.0.0               /dbfs/FileStore/Git/code-templates/src\n", "tabulate                      0.8.7\n", "tblib                         1.7.0\n", "tensorboard                   2.2.0\n", "tensorboard-plugin-wit        1.7.0\n", "tensorflow                    2.2.0\n", "tensorflow-estimator          2.2.0\n", "tensorflow-probability        0.10.1\n", "termcolor                     1.1.0\n", "terminado                     0.9.1\n", "testfixtures                  6.14.1\n", "testpath                      0.4.4\n", "text-unidecode                1.3\n", "Theano                        1.0.4\n", "threadpoolctl                 2.1.0\n", "tifffile                      2020.10.1\n", "toml                          0.10.1\n", "toolz                         0.11.1\n", "tornado                       6.0.4\n", "TPOT                          0.11.5\n", "tqdm                          4.48.2\n", "traitlets                     5.0.5\n", "typed-ast                     1.4.1\n", "typing-extensions             3.7.4.3\n", "tzlocal                       2.1\n", "Unidecode                     1.1.1\n", "update-checker                0.18.0\n", "urllib3                       1.25.11\n", "wcwidth                       0.2.5\n", "webencodings                  0.5.1\n", "websocket-client              0.57.0\n", "Werkzeug                      1.0.1\n", "wheel                         0.35.1\n", "widgetsnbextension            3.5.1\n", "wrapt                         1.12.1\n", "xarray                        0.16.1\n", "xgboost                       1.2.0\n", "xlrd                          1.2.0\n", "XlsxWriter                    1.3.8\n", "xverse                        1.0.5\n", "yellowbrick                   1.1\n", "zict                          2.0.0\n", "zipp                          3.3.1\n", "</div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "data": "<div class=\"ansiout\">Package                       Version             Location\n----------------------------- ------------------- --------------------------------------\nabsl-py                       0.10.0\nalabaster                     0.7.12\nalembic                       1.4.3\naltair                        4.1.0\nappdirs                       1.4.4\nargon2-cffi                   20.1.0\narviz                         0.10.0\nasn1crypto                    1.4.0\nastunparse                    1.6.3\nattrs                         20.1.0\nazure-core                    1.8.2\nazure-storage-blob            12.5.0\nBabel                         2.8.0\nbackcall                      0.2.0\nbandit                        1.6.2\nblack                         19.10b0\nbleach                        3.2.1\nblinker                       1.4\nbokeh                         2.2.3\nbrotlipy                      0.7.0\ncachetools                    4.2.2\ncategory-encoders             2.2.2\ncertifi                       2020.6.20\ncffi                          1.14.0\ncftime                        1.2.1\nchardet                       3.0.4\nclick                         7.1.2\ncloudpickle                   1.3.0\ncolorcet                      2.0.2\nconfigparser                  5.0.1\ncryptography                  3.1.1\ncycler                        0.10.0\ncytoolz                       0.11.0\ndask                          2.30.0\ndatabricks-cli                0.9.1\ndatashader                    0.11.1\ndatashape                     0.5.4\ndeap                          1.3.1\ndecorator                     4.4.2\ndefusedxml                    0.6.0\ndistributed                   2.30.0\ndocker                        4.3.1\ndocker-pycreds                0.4.0\ndocutils                      0.16\ndparse                        0.5.1\neli5                          0.10.1\nentrypoints                   0.3\nfastprogress                  1.0.0\nflake8                        3.8.3\nflake8-bandit                 2.1.2\nflake8-black                  0.2.1\nflake8-docstrings             1.5.0\nflake8-isort                  3.0.1\nflake8-polyfill               1.0.2\nFlask                         1.1.2\nfsspec                        0.7.4\nfuture                        0.16.0\ngast                          0.3.2\ngitdb                         4.0.5\nGitPython                     3.1.3\ngoogle-auth                   1.22.1\ngoogle-auth-oauthlib          0.4.1\ngoogle-pasta                  0.2.0\ngorilla                       0.3.0\ngraphviz                      0.14\ngreat-expectations            0.13.4\ngrpcio                        1.32.0\ngunicorn                      20.0.4\nh5py                          2.10.0\nHeapDict                      1.0.1\nholoviews                     1.13.4\nhvplot                        0.6.0\nhypothesis                    5.20.3\nidna                          2.10\nimageio                       2.9.0\nimagesize                     1.2.0\nimportlib-metadata            2.0.0\niniconfig                     1.0.1\ninvoke                        1.3.0\nipykernel                     5.3.4\nipython                       7.18.1\nipython-genutils              0.2.0\nipywidgets                    7.5.1\nisodate                       0.6.0\nisort                         4.3.21\nitsdangerous                  1.1.0\njedi                          0.17.2\nJinja2                        2.11.2\njoblib                        0.17.0\njson5                         0.9.5\njsonpatch                     1.32\njsonpointer                   2.1\njsonschema                    3.2.0\njupyter-client                6.1.7\njupyter-core                  4.6.3\njupyter-sphinx                0.3.1\njupyterlab                    2.2.6\njupyterlab-server             1.2.0\nKeras-Preprocessing           1.1.2\nkiwisolver                    1.2.0\nllvmlite                      0.34.0\nlocket                        0.2.0\nluminol                       0.4\nlxml                          4.6.1\nMako                          1.1.3\nMarkdown                      3.3.2\nMarkupSafe                    1.1.1\nmatplotlib                    3.3.2\nmccabe                        0.6.1\nmistune                       0.8.4\nmkl-fft                       1.2.0\nmkl-random                    1.1.1\nmkl-service                   2.3.0\nmlxtend                       0.17.3\nmore-itertools                8.5.0\nmsgpack                       1.0.0\nmsrest                        0.6.19\nmultipledispatch              0.6.0\nmypy-extensions               0.4.3\nnatsort                       7.0.1\nnbconvert                     5.6.1\nnbformat                      5.0.7\nnbsphinx                      0.7.1\nnetCDF4                       1.5.3\nnetworkx                      2.5\nnose                          1.3.7\nnotebook                      6.1.4\nnumba                         0.51.2\nnumpy                         1.19.1\noauthlib                      3.1.0\nolefile                       0.46\nopt-einsum                    3.3.0\npackaging                     20.4\npandas                        1.0.5\npandas-flavor                 0.2.0\npandocfilters                 1.4.2\npanel                         0.9.7\nparam                         1.9.3\nparso                         0.7.0\npartd                         1.1.0\npathspec                      0.7.0\npatsy                         0.5.1\npbr                           5.5.0\npexpect                       4.8.0\npickleshare                   0.7.5\nPillow                        8.0.0\npip                           20.2.2\npluggy                        0.13.1\nply                           3.11\npockets                       0.9.1\nprometheus-client             0.8.0\nprometheus-flask-exporter     0.18.1\nprompt-toolkit                3.0.8\nprotobuf                      3.13.0\npsutil                        5.7.2\nptyprocess                    0.6.0\npy                            1.9.0\npyarrow                       0.15.1\npyasn1                        0.4.8\npyasn1-modules                0.2.8\npycodestyle                   2.6.0\npycparser                     2.20\npyct                          0.4.8\npydocstyle                    5.1.1\npyflakes                      2.2.0\nPygments                      2.7.1\npygpu                         0.7.6\npyjanitor                     0.20.9\nPyJWT                         1.7.1\npymc3                         3.9.3\nPyomo                         5.7\npyOpenSSL                     19.1.0\npyparsing                     2.4.7\npyrsistent                    0.17.3\nPySocks                       1.7.1\npytest                        6.0.1\npython-dateutil               2.8.1\npython-editor                 1.0.4\npython-slugify                3.0.4\npytz                          2020.1\nPyUtilib                      6.0.0\npyviz-comms                   0.7.6\nPyWavelets                    1.1.1\nPyYAML                        5.3.1\npyzmq                         19.0.2\nquerystring-parser            1.2.4\nregex                         2020.7.14\nrequests                      2.23.0\nrequests-oauthlib             1.3.0\nrsa                           4.6\nruamel.yaml                   0.17.2\nruamel.yaml.clib              0.2.2\nruptures                      1.1.3\nsafety                        1.9.0\nscikit-image                  0.17.2\nscikit-learn                  0.23.2\nscipy                         1.5.2\nseaborn                       0.10.1\nSend2Trash                    1.5.0\nsetuptools                    50.3.0.post20201006\nshap                          0.35.0\nsingledispatch                *******\nsix                           1.15.0\nsmmap                         3.0.4\nsnowballstemmer               2.0.0\nsortedcontainers              2.2.2\nSphinx                        3.2.1\nsphinx-rtd-theme              0.5.0\nsphinxcontrib-applehelp       1.0.2\nsphinxcontrib-devhelp         1.0.2\nsphinxcontrib-htmlhelp        1.0.3\nsphinxcontrib-jsmath          1.0.1\nsphinxcontrib-napoleon        0.7\nsphinxcontrib-qthelp          1.0.3\nsphinxcontrib-serializinghtml 1.1.4\nSQLAlchemy                    1.3.13\nsqlparse                      0.4.1\nstatsmodels                   0.11.1\nstevedore                     3.2.2\nstopit                        1.1.2\nta-lib                        1.0.0               /dbfs/FileStore/Git/code-templates/src\ntabulate                      0.8.7\ntblib                         1.7.0\ntensorboard                   2.2.0\ntensorboard-plugin-wit        1.7.0\ntensorflow                    2.2.0\ntensorflow-estimator          2.2.0\ntensorflow-probability        0.10.1\ntermcolor                     1.1.0\nterminado                     0.9.1\ntestfixtures                  6.14.1\ntestpath                      0.4.4\ntext-unidecode                1.3\nTheano                        1.0.4\nthreadpoolctl                 2.1.0\ntifffile                      2020.10.1\ntoml                          0.10.1\ntoolz                         0.11.1\ntornado                       6.0.4\nTPOT                          0.11.5\ntqdm                          4.48.2\ntraitlets                     5.0.5\ntyped-ast                     1.4.1\ntyping-extensions             3.7.4.3\ntzlocal                       2.1\nUnidecode                     1.1.1\nupdate-checker                0.18.0\nurllib3                       1.25.11\nwcwidth                       0.2.5\nwebencodings                  0.5.1\nwebsocket-client              0.57.0\nWerkzeug                      1.0.1\nwheel                         0.35.1\nwidgetsnbextension            3.5.1\nwrapt                         1.12.1\nxarray                        0.16.1\nxgboost                       1.2.0\nxlrd                          1.2.0\nXlsxWriter                    1.3.8\nxverse                        1.0.5\nyellowbrick                   1.1\nzict                          2.0.0\nzipp                          3.3.1\n</div>", "datasetInfos": [], "removedWidgets": [], "type": "html"}}, "output_type": "display_data"}], "source": ["%sh\n", "pip list"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"inputWidgets": {}, "nuid": "8d254b1a-076e-4025-84de-500d63260bcd", "showTitle": false, "title": ""}}, "outputs": [{"data": {"text/html": ["<style scoped>\n", "  .an<PERSON>ut {\n", "    display: block;\n", "    unicode-bidi: embed;\n", "    white-space: pre-wrap;\n", "    word-wrap: break-word;\n", "    word-break: break-all;\n", "    font-family: \"Source Code Pro\", \"Menlo\", monospace;;\n", "    font-size: 13px;\n", "    color: #555;\n", "    margin-left: 4px;\n", "    line-height: 19px;\n", "  }\n", "</style>"]}, "metadata": {"application/vnd.databricks.v1+output": {"arguments": {}, "data": "", "errorSummary": "", "type": "ipynbError"}}, "output_type": "display_data"}], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "language": "python", "notebookMetadata": {"experimentId": "2577728460343403", "pythonIndentUnit": 2}, "notebookName": "01_automl", "notebookOrigID": 3802253373203144, "widgets": {}}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 1}