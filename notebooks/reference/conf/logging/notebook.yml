version: 1

disable_existing_loggers: False

formatters:
    default:
        format: "%(asctime)s — %(name)s — %(levelname)s — %(funcName)s:%(lineno)d — %(message)s"
        datefmt: '%Y-%m-%d %H:%M:%S'
    simple:
        format: "%(message)s"

handlers:
    console_handler:
        class: logging.StreamHandler
        level: DEBUG
        formatter: simple

root:
    level: WARN
    handlers:
        - console_handler
    propagate: yes
    
loggers:
    ta_lib:
        level: WARN
        handlers:
            - console_handler
        propagate: yes
    tigerml:
        level: WARN
        handlers:
            - console_handler
        propagate: yes
