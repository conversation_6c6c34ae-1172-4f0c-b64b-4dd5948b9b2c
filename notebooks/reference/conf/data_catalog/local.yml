datasets:
  raw:
    orders:
      type: ds
      format: csv
      uri: ${core.data_base_path}/raw/sales/orders.csv
      driver_params: {}
  
    product:
      type: ds
      format: csv
      uri: ${core.data_base_path}/raw/sales/prod_master.csv
      driver_params: {}
  
  cleaned:
    orders:
      type: ds
      format: parquet
      uri: ${core.data_base_path}/cleaned/sales/orders.parquet
      driver_params: {}

    product:
      type: ds
      format: parquet
      uri: ${core.data_base_path}/cleaned/sales/product.parquet
      driver_params: {}

    sales:
      type: ds
      format: parquet
      uri: ${core.data_base_path}/cleaned/sales/sales.parquet
      driver_params: {}

  processed:
    sales:
      type: ds
      format: parquet
      uri: ${core.data_base_path}/processed/sales/sales.parquet
      driver_params: {}

  train:
    sales:
      features:
        type: ds
        format: parquet
        uri: ${core.data_base_path}/train/sales/features.parquet
        driver_params:
          save:
            index: False
      target:
        type: ds
        format: parquet
        uri: ${core.data_base_path}/train/sales/target.parquet
        driver_params:
          save:
            index: False
  test:
    sales:
      features:
        type: ds
        format: parquet
        uri: ${core.data_base_path}/test/sales/features.parquet
        driver_params:
          save:
            index: False
      target:
        type: ds
        format: parquet
        uri: ${core.data_base_path}/test/sales/target.parquet
        driver_params:
          save:
            index: False
  score:
    sales:
      output:
        type: ds
        format: parquet
        uri: ${core.data_base_path}/test/sales/scored_output.parquet
        driver_params:
          save:
            index: False