<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="content-type" content="text/html" charset="utf-8">
    <title>{{report_title}}</title>
    <style>{{custom_style}}</style>
	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.css">
	<script src="https://code.jquery.com/jquery-3.4.1.min.js"
	integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo=" crossorigin="anonymous"></script>
	<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.js"></script>
	<script>{{custom_script}}</script>
	{{report_head}}
</head>
<body>
    <div class="overlay" id="confirm_delete">
		<div class="glass"></div>
        <div class="overlay_inner">
		    <div class="header">Confirm Delete?</div>
		    <div class="content">Are you sure you want to remove <b><span class="name"></span></b> from the report?
		        <!--<br> CAUTION: This cannot be undone.-->
			</div>
		    <div class="footer">
		        <div class="ctas">
		            <button class="cta inverse cancel">Cancel</button>
		            <button class="cta primary confirm">Confirm</button>
		        </div>
		    </div>
        </div>
    </div>
	<div class="banner" id="save_changes" style="z-index: 2;">
		<div class="content">
			The changes are unsaved. Click on <b>Save</b> to download the modified report.
		</div>
		<div class="ctas">
			<button class="confirm inverse cta">Save</button>
		</div>
    </div>
	<div class="banner" id="report_saved">
		<div class="content">
			Saved! Please check your downloads for the new report.
			<b>Make sure to have the new report in the same directory as the current report for it to work.</b>
		</div>
		<div class="ctas">
			<button class="cancel inverse cta">Okay</button>
		</div>
    </div>
    <div class="side_nav"><ul class="side_nav_inner">{{report_nav}}</ul></div>
    <div class="report_body">{{report_body}}</div>
</body>
</html>

