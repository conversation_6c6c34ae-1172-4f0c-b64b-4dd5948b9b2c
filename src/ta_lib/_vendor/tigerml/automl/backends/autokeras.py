# coding: utf-8
"""
Created on Mon Sep 17 15:23:07 2018.

@author: ranjith.a
"""


class ImageClassifier:
    """Imageclassifier class."""

    def __init__(self):
        pass

    def set_config(self):
        """Sets config for Imageclassifier class."""
        return self

    def get_config(self):
        """Gets config for Imageclassifier class."""
        return self

    def fit(self):
        """Fits for Imageclassifier class."""
        return self

    def score(self):
        """Scores for Imageclassifier class."""
        return self


class ImageRegressor:
    """Imageregressor class initializer."""

    def __init__(self):
        pass

    def set_config(self):
        """Sets config for Imageregressor class."""
        return self

    def get_config(self):
        """Gets config for Imageregressor class."""
        return self

    def fit(self):
        """Fits for Imageregressor class."""
        return self

    def score(self):
        """Scores for Imageregressor class."""
        return self


class TextClassifier:
    """Textclassifier class initializer."""

    def __init__(self):
        pass

    def set_config(self):
        """Sets config for Textclassifier class."""
        return self

    def get_config(self):
        """Gets config for Textclassifier class."""
        return self

    def fit(self):
        """Fits for Textclassifier class."""
        return self

    def score(self):
        """Scores for Textclassifier class."""
        return self


class TextRegressor:
    """Textregressor class initializer."""

    def __init__(self):
        pass

    def set_config(self):
        """Sets config for Textregressor class."""
        return self

    def get_config(self):
        """Gets config for Textregressor class."""
        return self

    def fit(self):
        """Fits Textregressor class."""
        return self

    def score(self):
        """Scores for Textregressor class."""
        return self
