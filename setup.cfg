[flake8]
max-line-length = 80
max-complexity = 12
ignore = E501, D104, D103, D100, D202, W503, E203
exclude = .bandit, .git, api.py, .ipynb_checkpoints, ta_lib/__init__.py,
          ta_lib/_version.py, setup.py, scripts, _ext_lib.py
docstring-convention = numpy
dictionaries = en_US,python,technical
per-file-ignores =
  **/tests/*.py: S101,
  src/ta_lib/core/utils.py: E712,
  src/ta_lib/core/tracking.py: BLK100,
  src/ta_lib/pyspark/*.py: BLK100,
  src/ta_lib/ebo/dtw_forcasting.py: C901,
  src/ta_lib/ebo/trends_identification/ti_clean_polygram_phrases.py: C901,
  src/ta_lib/ebo/trends_identification/ti_keyword_extraction.py: C901, I003,
  src/ta_lib/ebo/trends_identification/__init__.py: F401,
  src/ta_lib/pyspark/*.py: F401,
  src/ta_lib/pyspark/core/*.py: BLK100,
  src/ta_lib/mmx/*/__init__.py: F401,


[isort]
multi_line_output=3
include_trailing_comma=True
force_grid_wrap=0
use_parentheses=True
line_length=80
known_standard_library=posixpath

[tool:pytest]
markers=
  sanity: sanity tests
  functional: functional tests
