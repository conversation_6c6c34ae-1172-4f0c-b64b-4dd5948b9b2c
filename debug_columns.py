#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.abspath('.'))
sys.path.append(os.path.abspath('./src'))

import pandas as pd
import joblib

# Load the curated columns
curated_columns = joblib.load("artifacts/curated_columns.joblib")
print(f"Curated columns ({len(curated_columns)}): {curated_columns}")

# Load the feature transformer
features_transformer = joblib.load("artifacts/features.joblib")
print(f"Feature transformer type: {type(features_transformer)}")
print(f"Pipeline steps: {features_transformer.named_steps.keys()}")

# Load some test data to see what happens
from ta_lib.core.api import load_dataset, initialize_environment

context = initialize_environment("production/conf")
test_X = load_dataset(context, "test/housing/features")
print(f"Original test data columns: {list(test_X.columns)}")

# Transform with the pipeline
transformed = features_transformer.transform(test_X)
print(f"Transformed data shape: {transformed.shape}")
print(f"Transformed data type: {type(transformed)}")

if hasattr(transformed, 'columns'):
    print(f"Transformed columns: {list(transformed.columns)}")
    duplicates = pd.Series(transformed.columns).duplicated()
    if duplicates.any():
        print(f"Duplicate columns: {pd.Series(transformed.columns)[duplicates].tolist()}")
        print(f"All columns with counts:")
        for col in set(transformed.columns):
            count = list(transformed.columns).count(col)
            if count > 1:
                print(f"  {col}: {count} times")
else:
    print("Transformed data is not a DataFrame")
    
# Check what the housing transformer creates
housing_transformer = features_transformer.named_steps['housing_features']
housing_transformed = housing_transformer.transform(test_X)
print(f"Housing transformer output columns: {list(housing_transformed.columns)}")
