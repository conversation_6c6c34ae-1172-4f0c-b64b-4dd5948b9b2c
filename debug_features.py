#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.abspath('.'))
sys.path.append(os.path.abspath('./src'))

import pandas as pd
from ta_lib.core.api import initialize_environment, load_dataset, load_pipeline
from production.transformers import HousingFeatureTransformer

# Initialize environment
context = initialize_environment("production/conf")

# Load test data
test_X = load_dataset(context, "test/housing/features")
print(f"Original test data shape: {test_X.shape}")
print(f"Original columns: {list(test_X.columns)}")

# Load the feature pipeline
features_transformer = load_pipeline(context, "features")
print(f"Pipeline type: {type(features_transformer)}")
print(f"Pipeline steps: {features_transformer.named_steps.keys()}")

# Test the HousingFeatureTransformer step
housing_transformer = features_transformer.named_steps['housing_features']
housing_transformed = housing_transformer.transform(test_X)
print(f"After housing transformer shape: {housing_transformed.shape}")
print(f"After housing transformer columns: {list(housing_transformed.columns)}")

# Test the full pipeline
full_transformed = features_transformer.transform(test_X)
print(f"After full pipeline shape: {full_transformed.shape}")
print(f"After full pipeline type: {type(full_transformed)}")

# Check if it's a DataFrame
if hasattr(full_transformed, 'columns'):
    print(f"Full pipeline columns: {list(full_transformed.columns)}")
    # Check for duplicates
    duplicates = pd.Series(full_transformed.columns).duplicated()
    if duplicates.any():
        print(f"Duplicate columns found: {pd.Series(full_transformed.columns)[duplicates].tolist()}")
else:
    print("Full pipeline output is not a DataFrame")
