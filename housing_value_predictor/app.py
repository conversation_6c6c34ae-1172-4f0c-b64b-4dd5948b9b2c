"""
Flask web application for housing price prediction.

This module provides a web interface and API for predicting housing prices
using the trained machine learning model.
"""

import os
from typing import Any, Dict

import joblib
import pandas as pd
from flask import Flask, jsonify, render_template_string, request

from housing_value_predictor.utils import setup_logging

# HTML template for the web interface
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Housing Price Predictor</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .result { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 4px; }
        .error { background-color: #f8d7da; color: #721c24; }
        .success { background-color: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Housing Price Predictor</h1>
        <p>Enter the housing features below to get a price prediction:</p>

        <form id="predictionForm">
            <div class="form-group">
                <label for="longitude">Longitude:</label>
                <input type="number" id="longitude" name="longitude" step="0.000001" value="-122.23" required>
            </div>

            <div class="form-group">
                <label for="latitude">Latitude:</label>
                <input type="number" id="latitude" name="latitude" step="0.000001" value="37.88" required>
            </div>

            <div class="form-group">
                <label for="housing_median_age">Housing Median Age:</label>
                <input type="number" id="housing_median_age" name="housing_median_age" min="0" max="100" value="41" required>
            </div>

            <div class="form-group">
                <label for="total_rooms">Total Rooms:</label>
                <input type="number" id="total_rooms" name="total_rooms" min="1" value="880" required>
            </div>

            <div class="form-group">
                <label for="total_bedrooms">Total Bedrooms:</label>
                <input type="number" id="total_bedrooms" name="total_bedrooms" min="1" value="129" required>
            </div>

            <div class="form-group">
                <label for="population">Population:</label>
                <input type="number" id="population" name="population" min="1" value="322" required>
            </div>

            <div class="form-group">
                <label for="households">Households:</label>
                <input type="number" id="households" name="households" min="1" value="126" required>
            </div>

            <div class="form-group">
                <label for="median_income">Median Income:</label>
                <input type="number" id="median_income" name="median_income" step="0.0001" min="0" value="8.3252" required>
            </div>

            <div class="form-group">
                <label for="ocean_proximity">Ocean Proximity:</label>
                <select id="ocean_proximity" name="ocean_proximity" required>
                    <option value="NEAR BAY">Near Bay</option>
                    <option value="<1H OCEAN" selected>&lt;1H Ocean</option>
                    <option value="INLAND">Inland</option>
                    <option value="NEAR OCEAN">Near Ocean</option>
                    <option value="ISLAND">Island</option>
                </select>
            </div>

            <button type="submit">Predict Price</button>
        </form>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('predictionForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());

            // Convert numeric fields
            const numericFields = ['longitude', 'latitude', 'housing_median_age', 'total_rooms',
                                 'total_bedrooms', 'population', 'households', 'median_income'];
            numericFields.forEach(field => {
                data[field] = parseFloat(data[field]);
            });

            try {
                const response = await fetch('/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                const resultDiv = document.getElementById('result');

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `<h3>Prediction Result</h3><p><strong>Predicted Price: $${result.prediction.toLocaleString()}</strong></p>`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<h3>Error</h3><p>${result.error}</p>`;
                }

                resultDiv.style.display = 'block';
            } catch (error) {
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h3>Error</h3><p>Failed to get prediction: ${error.message}</p>`;
                resultDiv.style.display = 'block';
            }
        });
    </script>
</body>
</html>
"""


class HousingPricePredictor:
    """Housing price prediction model wrapper."""

    def __init__(self, model_dir: str = None):
        """Initialize the predictor with model artifacts.

        Args:
            model_dir: Directory containing model artifacts
        """
        if model_dir is None:
            # Use relative path from the app directory
            model_dir = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "artifacts"
            )
        self.model_dir = model_dir
        self.model = None
        self.preprocessing_pipeline = None
        self.logger = setup_logging()

    def load_model(self) -> None:
        """Load the trained model and preprocessing pipeline."""
        try:
            model_path = os.path.join(self.model_dir, "model.joblib")
            pipeline_path = os.path.join(
                self.model_dir, "preprocessing_pipeline.joblib"
            )

            if not os.path.exists(model_path):
                raise FileNotFoundError(f"Model file not found: {model_path}")
            if not os.path.exists(pipeline_path):
                raise FileNotFoundError(
                    f"Preprocessing pipeline not found: {pipeline_path}"
                )

            self.model = joblib.load(model_path)
            self.preprocessing_pipeline = joblib.load(pipeline_path)
            self.logger.info("Model and preprocessing pipeline loaded successfully")

        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise

    def predict(self, features: Dict[str, Any]) -> float:
        """Make a prediction for the given features.

        Args:
            features: Dictionary containing housing features

        Returns:
            Predicted housing price
        """
        if self.model is None or self.preprocessing_pipeline is None:
            raise RuntimeError("Model not loaded. Call load_model() first.")

        try:
            # Convert features to DataFrame
            df = pd.DataFrame([features])

            # Apply preprocessing
            X_prepared = self.preprocessing_pipeline.transform(df)

            # Make prediction
            prediction = self.model.predict(X_prepared)[0]

            self.logger.info(f"Prediction made: {prediction}")
            return float(prediction)

        except Exception as e:
            self.logger.error(f"Prediction failed: {e}")
            raise


# Initialize Flask app
app = Flask(__name__)
predictor = HousingPricePredictor()

# Load model at startup
with app.app_context():
    predictor.load_model()


@app.route("/")
def home():
    """Serve the main web interface."""
    return render_template_string(HTML_TEMPLATE)


@app.route("/health")
def health():
    """Health check endpoint."""
    return jsonify({"status": "healthy", "model_loaded": predictor.model is not None})


@app.route("/predict", methods=["POST"])
def predict():
    """API endpoint for making predictions."""
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate required fields
        required_fields = [
            "longitude",
            "latitude",
            "housing_median_age",
            "total_rooms",
            "total_bedrooms",
            "population",
            "households",
            "median_income",
            "ocean_proximity",
        ]

        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            return jsonify({"error": f"Missing required fields: {missing_fields}"}), 400

        # Make prediction
        prediction = predictor.predict(data)

        return jsonify({"prediction": prediction, "input_features": data})

    except Exception as e:
        app.logger.error(f"Prediction error: {e}")
        return jsonify({"error": str(e)}), 500


if __name__ == "__main__":
    # Load model
    predictor.load_model()

    # Run the app
    port = int(os.environ.get("PORT", 5000))
    app.run(host="0.0.0.0", port=port, debug=False)
