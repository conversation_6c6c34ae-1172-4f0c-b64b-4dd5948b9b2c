"""
Scores a trained housing value prediction model.

This module loads a trained model and associated preprocessing pipeline,
prepares test data, generates predictions, and evaluates the model using
metrics like RMSE and MAE. It can save the predictions and metrics to files.
"""

import argparse
import logging
import os
from typing import Any, Dict, Tuple

import joblib
import mlflow
import numpy as np
import pandas as pd
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.pipeline import Pipeline

from housing_value_predictor.config import (
    ARTIFACTS_DIR,
    DEFAULT_LOG_LEVEL,
    PROCESSED_DATA_DIR,
)
from housing_value_predictor.utils import setup_logging


def load_model_artifacts(
    model_dir: str, logger: logging.Logger
) -> Tuple[Any, Pipeline]:
    """Load the trained model and preprocessing pipeline from disk.

    Args:
        model_dir (str): Directory containing model artifacts.
        logger (logging.Logger): Logger instance for logging messages.

    Returns:
        Tuple[Any, Pipeline]: A tuple containing:
            - model: Trained model object.
            - preprocessing_pipeline: Fitted preprocessing pipeline.

    Raises:
        FileNotFoundError: If model or pipeline files are not found in `model_dir`.
    Example:
        >>> # model, Pipeline = load_model_artifacts("/path/to/artifacts", logger)
    """
    logger.info(f"Loading model artifacts from {model_dir}")
    model_path = os.path.join(model_dir, "model.joblib")
    pipeline_path = os.path.join(model_dir, "preprocessing_pipeline.joblib")

    model = joblib.load(model_path)
    preprocessing_pipeline = joblib.load(pipeline_path)

    logger.info(f"Loaded model type: {type(model).__name__}")
    logger.info(
        f"Loaded preprocessing pipeline type: {type(preprocessing_pipeline).__name__}"
    )

    return model, preprocessing_pipeline


def prepare_features(
    X: pd.DataFrame, preprocessing_pipeline: Pipeline, logger: logging.Logger
) -> np.ndarray:
    """Prepare features for prediction using the saved preprocessing pipeline.

    This function applies the pre-fitted preprocessing pipeline to the input data,
    which includes:
    1. Imputing missing values
    2. Adding engineered features (rooms_per_household, etc.)
    3. Standardizing numerical features
    4. One-hot encoding categorical features

    Args:
        X (pd.DataFrame): DataFrame of raw features (test set).
        preprocessing_pipeline (Pipeline): Fitted preprocessing pipeline.
        logger (logging.Logger): Logger instance for logging messages.

    Returns:
        np.ndarray: Array with prepared features ready for prediction.
    """
    logger.info("Preparing features for prediction using preprocessing pipeline")

    # Apply the preprocessing pipeline
    X_prepared = preprocessing_pipeline.transform(X)

    logger.info(f"Prepared features shape: {X_prepared.shape}")

    return X_prepared


def score_model(
    model: Any, X: np.ndarray, y: pd.Series, logger: logging.Logger
) -> Tuple[np.ndarray, Dict[str, float]]:
    """Score the model and return predictions and evaluation metrics.

    Calculates Root Mean Squared Error (RMSE) and Mean Absolute Error (MAE).

    Args:
        model (Any): Trained model object with a `predict` method.
        X (np.ndarray): Array of prepared features for scoring.
        y (pd.Series): Series of true target values.
        logger (logging.Logger): Logger instance for logging messages.

    Returns:
        Tuple[np.ndarray, Dict[str, float]]: A tuple containing:
            - predictions (np.ndarray): Array of model predictions.
            - metrics (Dict[str, float]): Dictionary with metric names (e.g., "rmse", "mae")
                                          and their calculated values.
    """
    logger.info("Generating predictions and calculating metrics")
    logger.info(f"Input feature shape: {X.shape}")
    predictions = model.predict(X)

    metrics = {
        "rmse": np.sqrt(mean_squared_error(y, predictions)),
        "mae": mean_absolute_error(y, predictions),
    }

    for metric_name, value in metrics.items():
        logger.info(f"{metric_name.upper()}: {value:.2f}")

    return predictions, metrics


def main(args: argparse.Namespace) -> None:
    """Main function to score the model and save results.

    Orchestrates the scoring pipeline:
    1. Sets up logging.
    2. Loads the test dataset.
    3. Loads the trained model and imputer.
    4. Prepares the test features.
    5. Generates predictions and calculates evaluation metrics.
    6. Optionally saves predictions and metrics to CSV files.

    Args:
        args (argparse.Namespace): Parsed command-line arguments containing:
            model_dir (str): Directory containing model artifacts.
            input_dir (str): Directory containing the test dataset ('test.csv').
            output_dir (Optional[str]): Directory to save predictions and metrics.
            log_level (str): Logging level.
            log_path (Optional[str]): Path to the log file.
            no_console_log (bool): Whether to disable console logging.
            parent_run_id (Optional[str]): MLflow parent run ID for nested runs.
    """
    # Setup logging
    logger = setup_logging(
        log_level=args.log_level,
        log_file=args.log_path,
        console_log=not args.no_console_log,
    )

    # Set up MLflow tracking
    run_name = "model_scoring"

    # Convert command line args to a dictionary for easier access
    args_dict = vars(args)
    parent_run_id = args_dict.get("parent_run_id")

    # Log the parent run ID for debugging
    logger.info(f"Parent run ID: {parent_run_id}")

    # Always create a new run
    if parent_run_id:
        # Create a nested run if parent_run_id is provided
        logger.info(f"Starting nested run with parent ID: {parent_run_id}")
        with mlflow.start_run(
            run_name=run_name, nested=True, tags={"mlflow.parentRunId": parent_run_id}
        ):
            return _run_model_scoring(args, logger)
    else:
        # Create a standalone run if no parent_run_id
        logger.info("Starting standalone run (no parent ID)")
        with mlflow.start_run(run_name=run_name):
            return _run_model_scoring(args, logger)


def _run_model_scoring(args: argparse.Namespace, logger: logging.Logger) -> None:
    """Execute the model scoring process with MLflow tracking."""
    # Log MLflow information for debugging
    logger.info(f"MLflow tracking URI: {mlflow.get_tracking_uri()}")
    logger.info(f"MLflow run ID: {mlflow.active_run().info.run_id}")

    # Log parameters
    mlflow.log_param("model_dir", args.model_dir)
    mlflow.log_param("input_dir", args.input_dir)
    mlflow.log_param("script_path", os.path.abspath(__file__))
    if args.output_dir:
        mlflow.log_param("output_dir", args.output_dir)

    # Load test data
    logger.info(f"Loading test data from {args.input_dir}")
    test_data = pd.read_csv(os.path.join(args.input_dir, "test.csv"))
    X_test = test_data.drop("median_house_value", axis=1)
    y_test = test_data["median_house_value"]

    # Log test data info
    mlflow.log_metric("test_set_size", len(X_test))

    # Load model and preprocessing pipeline
    model, preprocessing_pipeline = load_model_artifacts(args.model_dir, logger)

    # Prepare features using the pipeline
    X_test_prepared = prepare_features(X_test, preprocessing_pipeline, logger)

    # Generate predictions and metrics
    predictions, metrics = score_model(model, X_test_prepared, y_test, logger)

    # Log metrics
    for metric_name, metric_value in metrics.items():
        mlflow.log_metric(f"test_{metric_name}", metric_value)

    # Calculate and log additional metrics
    residuals = y_test - predictions
    mlflow.log_metric("test_mean_error", residuals.mean())
    mlflow.log_metric("test_median_error", np.median(residuals))
    mlflow.log_metric("test_min_error", residuals.min())
    mlflow.log_metric("test_max_error", residuals.max())
    mlflow.log_metric("test_error_std", residuals.std())

    # Create and log a residual plot
    import matplotlib.pyplot as plt

    plt.figure(figsize=(10, 6))
    plt.scatter(predictions, residuals, alpha=0.5)
    plt.axhline(y=0, color="r", linestyle="-")
    plt.xlabel("Predicted Values")
    plt.ylabel("Residuals")
    plt.title("Residual Plot")

    # Save the plot
    if args.output_dir:
        os.makedirs(args.output_dir, exist_ok=True)
        residual_plot_path = os.path.join(args.output_dir, "residual_plot.png")
        plt.savefig(residual_plot_path)
        plt.close()

        # Log the plot as an artifact
        mlflow.log_artifact(residual_plot_path, "plots")

    # Save predictions and metrics
    if args.output_dir:
        os.makedirs(args.output_dir, exist_ok=True)
        logger.info(f"Saving predictions and metrics to {args.output_dir}")

        # Save predictions
        predictions_path = os.path.join(args.output_dir, "predictions.csv")
        metrics_path = os.path.join(args.output_dir, "metrics.csv")

        pd.DataFrame({"true_values": y_test, "predictions": predictions}).to_csv(
            predictions_path, index=False
        )

        # Save metrics
        pd.DataFrame([metrics]).to_csv(metrics_path, index=False)

        # Log artifacts
        mlflow.log_artifact(predictions_path, "predictions")
        mlflow.log_artifact(metrics_path, "metrics")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Score housing price prediction model")
    parser.add_argument(
        "--model-dir",
        type=str,
        default=ARTIFACTS_DIR,
        help="Directory containing model artifacts",
    )
    parser.add_argument(
        "--input-dir",
        type=str,
        default=PROCESSED_DATA_DIR,
        help="Input directory containing test dataset",
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default=os.path.join(ARTIFACTS_DIR, "predictions"),
        help="Output directory for predictions and metrics",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default=DEFAULT_LOG_LEVEL,
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Set the logging level",
    )
    parser.add_argument(
        "--log-path",
        type=str,
        help="Path to log file. If not specified, logs will only be written to console",
    )
    parser.add_argument(
        "--no-console-log",
        action="store_true",
        help="Disable logging to console",
    )
    parser.add_argument(
        "--parent-run-id",
        dest="parent_run_id",  # Explicitly set the destination attribute name
        type=str,
        help="MLflow parent run ID for nested runs",
    )

    args = parser.parse_args()
    main(args)
