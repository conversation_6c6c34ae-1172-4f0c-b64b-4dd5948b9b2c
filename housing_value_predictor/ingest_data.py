"""
<PERSON>les downloading, preparing, and splitting the housing data.

This module provides functions to:
1. Download the housing dataset from a specified URL.
2. Preprocess the data, including handling missing values and creating income categories.
3. Split the data into training and testing sets using stratified sampling.
"""

import argparse
import logging
import os
import tarfile
from typing import Tuple

import mlflow
import numpy as np
import pandas as pd
from six.moves import urllib  # type: ignore
from sklearn.model_selection import StratifiedShuffleSplit

from housing_value_predictor.config import (
    DEFAULT_LOG_LEVEL,
    HOUSING_URL,
    PROCESSED_DATA_DIR,
)
from housing_value_predictor.utils import setup_logging


def download_data(url: str, output_path: str, logger: logging.Logger) -> None:
    """Download data from a URL and extract if it's a .tgz file.

    Args:
        url (str): The URL from which to download the data.
        output_path (str): The local file path to save the downloaded data.
                           If the downloaded file is a .tgz archive, it will be
                           extracted into the same directory as `output_path`.
        logger (logging.Logger): Logger instance for logging messages.

    Example:
        >>> import logging
        >>> logger = logging.getLogger("my_logger")
        >>> # Create a dummy tgz for example purposes if it doesn't exist
        >>> # download_data("http://example.com/data.tgz", "/tmp/data.tgz", logger)
    """
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    logger.info(f"Downloading data from {url} to {output_path}")
    urllib.request.urlretrieve(url, output_path)
    logger.info("Download completed")

    if output_path.endswith(".tgz"):
        logger.info("Extracting .tgz file...")
        with tarfile.open(output_path) as housing_tgz:
            housing_tgz.extractall(path=os.path.dirname(output_path))
        logger.info("Extraction completed")


def prepare_data(
    data_path: str, logger: logging.Logger
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Load, preprocess, and split the housing data.

    The preparation steps include:
    1. Imputing missing 'median_income' values with the median.
    2. Clipping 'median_income' to be non-negative.
    3. Creating 'income_cat' for stratified sampling based on 'median_income'.
    4. Dropping rows with NaN 'income_cat' values.
    5. Performing a stratified shuffle split to create training and test sets.
    6. Removing the temporary 'income_cat' column.

    Args:
        data_path (str): Path to the CSV file containing the housing data.
        logger (logging.Logger): Logger instance for logging messages.

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: A tuple containing the training DataFrame
                                           and the test DataFrame.
    Example:
        >>> # train_df, test_df = prepare_data("path/to/housing.csv", logger)
    """
    logger.info(f"Loading data from {data_path}")
    housing = pd.read_csv(data_path)

    # 1. Impute missing median_income and ensure non-negative
    logger.info("Handling missing values in 'median_income'")
    housing["median_income"] = housing["median_income"].fillna(
        housing["median_income"].median()
    )
    housing["median_income"] = housing["median_income"].clip(lower=0.0)

    # 2. Create income categories
    logger.info("Creating income categories for stratified sampling")
    housing["income_cat"] = pd.cut(
        housing["median_income"],
        bins=[0.0, 1.5, 3.0, 4.5, 6.0, np.inf],
        labels=[1, 2, 3, 4, 5],
    )

    # 3. Drop NaNs in income_cat and reset index
    nan_count = housing["income_cat"].isna().sum()
    if nan_count > 0:
        logger.warning(
            f"Found {nan_count} NaN values in 'income_cat'; dropping these rows before split."
        )
        housing = housing[housing["income_cat"].notna()].reset_index(drop=True)

    # 4. Stratified split
    logger.info("Performing stratified split")
    split = StratifiedShuffleSplit(n_splits=1, test_size=0.2, random_state=42)
    for train_idx, test_idx in split.split(housing, housing["income_cat"]):
        train_set = housing.iloc[train_idx].copy()
        test_set = housing.iloc[test_idx].copy()

    # 5. Remove helper column
    for dataset in (train_set, test_set):
        dataset.drop("income_cat", axis=1, inplace=True)

    return train_set, test_set


def main(args: argparse.Namespace) -> None:
    """Main function to download, process, and save housing data.

    Orchestrates the data ingestion pipeline:
    1. Sets up logging.
    2. Defines paths for raw and processed data.
    3. Downloads data if the raw CSV doesn't exist.
    4. Prepares and splits the data into training and test sets.
    5. Saves the processed training and test sets to CSV files.

    The script expects command-line arguments for output directory,
    log level, log path, and console logging.

    Args:
        args (argparse.Namespace): Parsed command-line arguments containing:
            output_dir (str): Directory to save processed datasets.
            log_level (str): Logging level.
            log_path (Optional[str]): Path to the log file.
            no_console_log (bool): Whether to disable console logging.
            parent_run_id (Optional[str]): MLflow parent run ID for nested runs.
    """
    logger = setup_logging(
        log_level=args.log_level,
        log_file=args.log_path,
        console_log=not args.no_console_log,
    )

    # Set up MLflow tracking
    run_name = "data_preparation"

    # Convert command line args to a dictionary for easier access
    args_dict = vars(args)
    parent_run_id = args_dict.get("parent_run_id")

    # Log the parent run ID for debugging
    logger.info(f"Parent run ID: {parent_run_id}")

    # Always create a new run
    if parent_run_id:
        # Create a nested run if parent_run_id is provided
        logger.info(f"Starting nested run with parent ID: {parent_run_id}")
        with mlflow.start_run(
            run_name=run_name, nested=True, tags={"mlflow.parentRunId": parent_run_id}
        ):
            return _run_data_preparation(args, logger)
    else:
        # Create a standalone run if no parent_run_id
        logger.info("Starting standalone run (no parent ID)")
        with mlflow.start_run(run_name=run_name):
            return _run_data_preparation(args, logger)


def _run_data_preparation(args: argparse.Namespace, logger: logging.Logger) -> None:
    """Execute the data preparation process with MLflow tracking."""
    # Log MLflow information for debugging
    logger.info(f"MLflow tracking URI: {mlflow.get_tracking_uri()}")
    logger.info(f"MLflow run ID: {mlflow.active_run().info.run_id}")

    # Log parameters
    mlflow.log_param("output_dir", args.output_dir)
    mlflow.log_param("data_url", HOUSING_URL)
    mlflow.log_param("script_path", os.path.abspath(__file__))

    tgz_path = os.path.join(args.output_dir, "housing.tgz")
    csv_path = os.path.join(args.output_dir, "housing.csv")

    if not os.path.exists(csv_path):
        download_data(HOUSING_URL, tgz_path, logger)

    train_set, test_set = prepare_data(csv_path, logger)

    # Log metrics
    mlflow.log_metric("train_set_size", len(train_set))
    mlflow.log_metric("test_set_size", len(test_set))
    mlflow.log_metric("train_test_ratio", len(train_set) / len(test_set))

    # Log additional dataset metrics
    mlflow.log_metric("total_samples", len(train_set) + len(test_set))
    mlflow.log_metric(
        "train_percentage", len(train_set) / (len(train_set) + len(test_set)) * 100
    )

    # Create and log data distribution plots
    import matplotlib.pyplot as plt
    import seaborn as sns

    # Create directory for plots if it doesn't exist
    plots_dir = os.path.join(args.output_dir, "eda_plots")
    os.makedirs(plots_dir, exist_ok=True)

    # Plot target variable distribution
    plt.figure(figsize=(10, 6))
    sns.histplot(train_set["median_house_value"], kde=True)
    plt.title("Distribution of Median House Value (Training Set)")
    plt.xlabel("Median House Value")
    plt.ylabel("Frequency")
    target_plot_path = os.path.join(plots_dir, "target_distribution.png")
    plt.savefig(target_plot_path)
    plt.close()

    # Log the plot as an artifact
    mlflow.log_artifact(target_plot_path, "data_plots")

    # Log dataset statistics
    for col in train_set.select_dtypes(include=[np.number]).columns:
        if col != "median_house_value":  # Skip target variable
            # Clean column name to ensure it's valid for MLflow
            safe_col = col.replace("<", "lt").replace(">", "gt").replace(" ", "_")
            mlflow.log_metric(f"train_{safe_col}_mean", train_set[col].mean())
            mlflow.log_metric(f"train_{safe_col}_median", train_set[col].median())

    logger.info("Saving processed datasets")
    os.makedirs(args.output_dir, exist_ok=True)
    train_path = os.path.join(args.output_dir, "train.csv")
    test_path = os.path.join(args.output_dir, "test.csv")

    train_set.to_csv(train_path, index=False)
    test_set.to_csv(test_path, index=False)

    # Log artifacts
    mlflow.log_artifact(train_path, "datasets")
    mlflow.log_artifact(test_path, "datasets")

    logger.info("Data processing completed")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Download and prepare housing data")
    parser.add_argument(
        "--output-dir",
        type=str,
        default=PROCESSED_DATA_DIR,
        help="Output directory for processed datasets",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default=DEFAULT_LOG_LEVEL,
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Set the logging level",
    )
    parser.add_argument(
        "--log-path",
        type=str,
        help="Path to log file. If not specified, logs will only be written to console",
    )
    parser.add_argument(
        "--no-console-log",
        action="store_true",
        help="Disable logging to console",
    )
    parser.add_argument(
        "--parent-run-id",
        dest="parent_run_id",  # Explicitly set the destination attribute name
        type=str,
        help="MLflow parent run ID for nested runs",
    )

    args = parser.parse_args()
    main(args)
