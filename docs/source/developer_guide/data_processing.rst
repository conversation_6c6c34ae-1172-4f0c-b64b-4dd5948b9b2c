===============
Data Processing
===============


Data Cleaning 
-------------

Document key data cleaning steps such as column name & datatype standardizations, discarded columns or filtered out data etc..

Data Consolidation
------------------

Since merging is a critical step and often leads to erroneous models/reruns, document the decisions taken while merging such as type of join, actions in cases of cardinality not as expected etc..


Imputation
----------

Describe the imputation rules and rational behind such rules. For example, filling missing gender with "unknown".

Outlier treatment
-----------------

Describe the actions taken to treat outlier such as capping, discarding etc..


Feature transformations
-----------------------

Describe any feature treatments such as encoding, PCA, scaling, log-normal etc..


Feature engineering
-------------------

This should be a rich section describing all the features engineered in the modeling process.