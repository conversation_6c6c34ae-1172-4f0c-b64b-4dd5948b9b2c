================
Project Overview
================



The library contains example code templates for common business problems encountered in DS engagements. These templates can be readily set up
in your local environment and used for your project. Code templates are available for Machine Learning problems like
`Regression, Classification and time series forecasting`.

Approach
========
The solution approach describes the general approach that will be taken to create or acquire the new capabilities required to meet the business need. To determine the solution approach, it is necessary to identify possible approaches, determine the means by which the solution may be delivered (including the methodology and lifecycle to be used) and assess whether the organization is capable of implementing and effectively using a solution of that nature.

Evaluation
==========
The project evaluation process has been around as long as there have been projects to evaluate. It uses systemic analysis to gather data and reveal the effectiveness and efficiency of the model.
    

Architecture
============
To add: describe dev architect, deployment architecture, model serving layers etc..

    


    