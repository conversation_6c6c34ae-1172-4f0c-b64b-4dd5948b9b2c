{{ fullname | escape | underline }}

.. rubric:: Description
.. automodule:: {{ fullname }}
.. currentmodule:: {{ fullname }}

{% block functions %}
{% if functions %}
.. rubric:: {{ _('Functions') }}

.. autosummary::
    :toctree:
{% for item in functions %}
    {{ item }}
{%- endfor %}
{% endif %}
{% endblock %}

{% block classes %}
{% if classes %}
.. rubric:: {{ _('Classes') }}

.. autosummary::
    :toctree: ../_autosummary/
    :template: class.rst
        
{% for item in classes %}
    {{ item }}
{%- endfor %}
{% endif %}
{% endblock %}

{% block exceptions %}
{% if exceptions %}
.. rubric:: {{ _('Exceptions') }}

.. autosummary::
    :toctree:  
                                    
{% for item in exceptions %}
    {{ item }}
{%- endfor %}
{% endif %}
{% endblock %}
