=======
Metrics
=======

.. _r2_score:

R² Score
--------
The coefficient of determination, denoted R², is a metric that indicates the proportion of the variance in the dependent variable that is predictable from the independent variables.

A value of 1 indicates that the regression predictions perfectly fit the data, while a value of 0 indicates that the model doesn't explain any of the variability of the response data around its mean.

.. _metadata_routing:

Metadata Routing
----------------
Metadata routing is a mechanism in scikit-learn that allows passing metadata to different steps in a pipeline. It enables more flexible control over how data is processed through the pipeline.
