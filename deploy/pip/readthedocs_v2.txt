absl-py==1.2.0
alabaster==0.7.12
alembic==1.8.1
altair==4.2.0
appdirs==1.4.4
arviz==0.12.1
asn1crypto==1.5.1
asttokens==2.0.8
astunparse==1.6.3
async-generator==1.10
attrs==22.1.0
Babel==2.10.3
backcall==0.2.0
backports.functools-lru-cache==1.6.4
beautifulsoup4==4.11.1
black==22.10.0
bleach==5.0.1
blinker==1.5
bokeh==2.4.3
brotlipy==0.7.0
cachetools==5.2.0
category-encoders==2.4.1
certifi==2022.9.24
cffi==1.15.1
cftime==1.6.2
chardet==3.0.4
charset-normalizer==2.1.1
click==8.1.3
cloudpickle==2.2.0
colorama==0.4.5
colorcet==3.0.1
configparser==5.3.0
cryptography==38.0.2
cycler==0.11.0
cytoolz==0.12.0
dask==2022.9.2
databricks-cli==0.17.3
datashader==0.13.0
datashape==0.5.2
deap==1.3.1
debugpy==1.6.3
decorator==5.1.1
defusedxml==0.7.1
distributed==2022.9.2
dm-tree==0.1.7
docker==5.0.3
docker-pycreds==0.4.0
docutils==0.17.1
entrypoints==0.4
et-xmlfile==1.0.1
exceptiongroup==1.0.0rc9
executing==1.1.1
fastjsonschema==2.16.2
Flask==2.2.2
flatbuffers==1.12
fonttools==4.37.4
fsspec==2022.7.1
future==0.16.0
gast==0.4.0
gitdb==4.0.9
GitPython==3.1.29
google-auth==2.12.0
google-auth-oauthlib==0.4.6
google-cloud-automl==2.4.2
google-pasta==0.2.0
grpcio==1.49.1
gunicorn==20.1.0
h11==0.14.0
h5py==3.7.0
HeapDict==1.0.1
humanfriendly==8.2
holoviews==1.14.9
hvplot==0.8.0
idna==2.10
imagesize==1.4.1
importlib-metadata==4.11.4
importlib-resources==5.10.0
invoke==1.7.3
ipykernel==6.16.0
ipython==8.3.0
ipywidgets==8.0.2
itsdangerous==2.1.2
jedi==0.18.1
Jinja2==3.0.3
joblib==1.2.0
jsonpatch==1.32
jsonpointer==2.0
jsonschema==4.16.0
jupyter-client==7.3.4
jupyter-core==4.10.0
jupyter-sphinx==0.4.0
jupyterlab-pygments==0.2.2
jupyterlab-widgets==3.0.3
keras==2.9.0
Keras-Preprocessing==1.1.2
kiwisolver==1.4.4
libclang==14.0.6
llvmlite==0.39.1
locket==1.0.0
luminol==0.4
lxml==4.6.1
lz4==4.0.0
Mako==1.2.3
Markdown==3.4.1
MarkupSafe==2.1.1
matplotlib==3.5.3
matplotlib-inline==0.1.6
mistune==2.0.4
mlflow==1.26.0
mlxtend==0.17.2
msgpack==1.0.4
multipledispatch==0.6.0
munkres==1.1.4
mypy-extensions==0.4.3
natsort==8.2.0
nbclient==0.7.0
nbconvert==7.2.1
nbformat==5.7.0
nbsphinx==0.8.8
nest-asyncio==1.5.6
netCDF4==1.6.1
numba==0.56.2
numpy==1.21.6
oauthlib==3.2.1
openpyxl==3.0.9
opt-einsum==3.3.0
outcome==1.2.0
packaging==21.3
pandas==1.4.2
pandas-flavor==0.2.0
pandocfilters==1.5.0
panel==0.13.1
param==1.12.1
parso==0.8.3
partd==1.3.0
pathspec==0.10.1
patsy==0.5.3
pexpect==4.8.0
pickleshare==0.7.5
Pillow==9.2.0
pip==22.2.2
pkgutil_resolve_name==1.3.10
platformdirs==2.5.2
ply==3.11
pockets==0.9.1
prometheus-client==0.14.1
prometheus-flask-exporter==0.20.3
prompt-toolkit==3.0.31
protobuf==3.19.4
psutil==5.9.2
ptyprocess==0.7.0
pure-eval==0.2.2
py4j==********
pyarrow==4.0.1
pyasn1==0.4.8
pyasn1-modules==0.2.8
pycparser==2.21
pyct==0.4.6
Pygments==2.13.0
pyjanitor==0.20.9
PyJWT==2.5.0
Pyomo==6.4.1
pyOpenSSL==22.1.0
pyparsing==2.4.7
PyQt5==5.15.7
PyQt5-sip==12.11.0
pyrsistent==0.18.1
PySocks==1.7.1
pyspark==3.2.0
pyspark-dist-explore==0.1.8
python-dateutil==2.8.2
python-dotenv==0.21.0
python-slugify==3.0.4
pytz==2022.4
pytz-deprecation-shim==0.1.0.post0
pyviz-comms==2.2.1
PyYAML==6.0
pyzmq==24.0.1
querystring-parser==1.2.4
requests==2.28.1
requests-oauthlib==1.3.1
rsa==4.9
ruamel.yaml==0.17.21
ruamel.yaml.clib==0.2.6
ruptures==1.1.6
scikit-learn==1.1.1
scipy==1.8.1
seaborn==0.11.2
selenium==4.3.0
setuptools==58.2.0
shap==0.40.0
sip==6.6.2
six==1.16.0
slicer==0.0.7
smmap==3.0.5
sniffio==1.3.0
snowballstemmer==2.2.0
sortedcontainers==2.4.0
soupsieve==2.3.2.post1
Sphinx==4.5.0
sphinx-rtd-theme==1.0.0
sphinxcontrib-applehelp==1.0.2
sphinxcontrib-devhelp==1.0.2
sphinxcontrib-htmlhelp==2.0.0
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-napoleon==0.7
sphinxcontrib-qthelp==1.0.3
sphinxcontrib-serializinghtml==1.1.5
SQLAlchemy==1.3.24
sqlparse==0.4.3
stack-data==0.5.1
statsmodels==0.13.2
stopit==1.1.2
tabulate==0.9.0
tblib==1.7.0
tensorboard==2.9.1
tensorboard-data-server==0.6.1
tensorboard-plugin-wit==1.8.1
tensorflow==2.9.1
tensorflow-estimator==2.9.0
tensorflow-io-gcs-filesystem==0.27.0
tensorflow-probability==0.17.0
termcolor==2.0.1
text-unidecode==1.3
threadpoolctl==3.1.0
tinycss2==1.1.1
toml==0.10.2
tomli==2.0.1
toolz==0.12.0
tornado==6.1
TPOT==0.11.7
tqdm==4.64.1
traitlets==5.4.0
trio==0.22.0
trio-websocket==0.9.2
typing_extensions==4.4.0
tzdata==2022.4
tzlocal==4.2
unicodedata2==14.0.0
Unidecode==1.3.6
update-checker==0.18.0
urllib3==1.26.12
urllib3-secure-extra==0.1.0
wcwidth==0.2.5
webdriver-manager==3.8.3
webencodings==0.5.1
websocket-client==1.4.1
Werkzeug==2.2.2
wheel==0.37.1
widgetsnbextension==4.0.3
wrapt==1.14.1
wsproto==1.2.0
xarray==2022.9.0
xarray-einstats==0.3.0
xgboost==1.5.1
xlrd==2.0.1
XlsxWriter==3.0.3
xmltodict==0.12.0
zict==2.2.0
zipp==3.9.0
-e "git+https://${GITHUB_OAUTH_TOKEN}@github.com/tigerrepository/TigerML.git@master#egg=tigerml&subdirectory=python"
-e "git+https://${GITHUB_OAUTH_TOKEN}@github.com/tigerrepository/BayesianLearning.git@master#egg=BayesFramework"