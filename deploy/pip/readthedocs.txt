alabaster==0.7.12
alembic==1.4.2
appdirs==1.4.4
argon2-cffi==20.1.0
attrs==19.3.0
azure-core==1.8.0
azure-storage-blob==12.3.2
Babel==2.8.0
backcall==0.2.0
bandit==1.6.2
black==19.10b0
bleach==3.1.5
bokeh==2.1.1
botocore==1.17.40
category-encoders==2.2.2
certifi==2020.6.20
cffi==1.14.1
chardet==3.0.4
click==7.1.2
colorcet==2.0.2
cryptography==3.0
cycler==0.10.0
dask==2.22.0
databricks-cli==0.11.0
datashader==0.11.0
datashape==0.5.2
deap==1.3.1
decorator==4.4.2
defusedxml==0.6.0
distributed==2.20.0
docker==4.3.0
docutils==0.15.2
eli5==0.10.1
entrypoints==0.3
flake8==3.8.3
flake8-bandit==2.1.2
flake8-black==0.2.1
flake8-docstrings==1.5.0
flake8-isort==3.0.1
flake8-polyfill==1.0.2
Flask==1.1.2
fsspec==0.7.4
gitdb==4.0.5
GitPython==3.1.7
gorilla==0.3.0
graphviz==0.14.1
gunicorn==20.0.4
HeapDict==1.0.1
holoviews==1.13.3
hvplot==0.6.0
hypothesis==5.20.4
idna==2.10
imagesize==1.2.0
iniconfig==1.0.1
invoke==1.3.1
ipykernel==5.3.4
ipython==7.17.0
ipython-genutils==0.2.0
isodate==0.6.0
isort==4.3.21
itsdangerous==1.1.0
jedi==0.17.2
Jinja2==2.11.2
jmespath==0.10.0
joblib==0.16.0
json5==0.9.5
jsonschema==3.2.0
jupyter-client==6.1.6
jupyter-core==4.6.3
jupyterlab==2.2.4
jupyterlab-server==1.2.0
jupyter-sphinx==0.3.1
kiwisolver==1.2.0
llvmlite==0.31.0
locket==0.2.0
Mako==1.1.3
Markdown==3.2.2
MarkupSafe==1.1.1
matplotlib==3.3.0
mccabe==0.6.1
mistune==0.8.4
mlflow==1.10.0
mlxtend==0.17.3
more-itertools==8.4.0
msgpack==1.0.0
msrest==0.6.18
multipledispatch==0.6.0
natsort==7.0.1
nbconvert==5.6.1
nbformat==5.0.7
nbsphinx==0.7.1
notebook==6.1.2
numba==0.48.0
oauthlib==3.1.0
packaging==20.4
pandas==1.0.5
pandas-flavor==0.2.0
pandocfilters==1.4.2
panel==0.9.7
param==1.9.3
parso==0.7.1
partd==1.1.0
pathspec==0.8.0
patsy==0.5.1
pbr==5.4.5
pexpect==4.8.0
pickleshare==0.7.5
Pillow==7.2.0
pluggy==0.13.1
pockets==0.9.1
prometheus-client==0.8.0
prometheus-flask-exporter==0.15.4
prompt-toolkit==3.0.6
psutil==5.7.2
ptyprocess==0.6.0
py==1.9.0
pycodestyle==2.6.0
pycparser==2.20
pyct==0.4.6
pydocstyle==5.0.2
pyflakes==2.2.0
Pygments==2.6.1
pyjanitor==0.20.8
pyparsing==2.4.7
pyrsistent==0.16.0
pytest==6.0.1
python-dateutil==2.8.1
python-editor==1.0.4
python-slugify==3.0.6
pytz==2020.1
pyviz-comms==0.7.6
PyYAML==5.3.1
pyzmq==19.0.2
querystring-parser==1.2.4
regex==2020.7.14
requests==2.24.0
requests-oauthlib==1.3.0
s3fs==0.4.2
scikit-learn==0.23.2
scipy==1.4.1
seaborn==0.10.1
Send2Trash==1.5.0
setuptools-scm==4.1.2
six==1.15.0
shap==0.35.0
smmap==3.0.4
snowballstemmer==2.0.0
sortedcontainers==2.2.2
Sphinx==3.2.0
sphinx-rtd-theme==0.5.0
sphinxcontrib-applehelp==1.0.2
sphinxcontrib-devhelp==1.0.2
sphinxcontrib-htmlhelp==1.0.3
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-napoleon==0.7
sphinxcontrib-qthelp==1.0.3
sphinxcontrib-serializinghtml==1.1.4
sphinxcontrib-websupport==1.1.2
humanfriendly==8.2
SQLAlchemy==1.3.13
sqlparse==0.3.1
statsmodels==0.11.1
stevedore==3.2.0
stopit==1.1.2
tabulate==0.8.7
tblib==1.7.0
terminado==0.8.3
testfixtures==6.14.1
testpath==0.4.4
text-unidecode==1.3
threadpoolctl==2.1.0
toml==0.10.1
toolz==0.10.0
tornado==6.0.4
TPOT==0.11.5
tqdm==4.48.2
traitlets==4.3.3
typed-ast==1.4.1
typing-extensions==*******
update-checker==0.18.0
urllib3==1.25.10
wcwidth==0.2.5
webencodings==0.5.1
websocket-client==0.57.0
Werkzeug==1.0.1
xarray==0.16.1
xgboost==1.1.1
xlrd==1.2.0
xverse==1.0.*
zict==2.0.0
ipywidgets==7.5.1
widgetsnbextension==3.5.1
importlib-metadata==2.0.0
zipp==3.3.1
arviz==0.10.0
pymc3==3.9.3
arviz==0.10.0
netcdf4==1.5.3
cftime==1.2.1
fastprogress==1.0.0
h5py==2.10.0
theano==1.0.5
pyomo==5.7
ply==3.11
pyutilib==6.0.0
nose==1.3.7
appdirs==1.4.4
cloudpickle==1.3.0 
gast==0.3.3
absl-py==0.10.0
astunparse==1.6.3
google-pasta==0.2.0
grpcio==1.32.0
keras-preprocessing==1.1.2
opt-einsum==3.3.0
pyasn1==0.4.8
rsa==4.6
pyasn1-modules==0.2.8
google-auth==1.22.1
google-auth-oauthlib==0.4.1
oauthlib==3.1.0
requests-oauthlib==1.3.0
protobuf==3.13.0
tensorboard-plugin-wit==1.7.0
werkzeug==1.0.1
wheel==0.35.1
tensorflow-estimator==2.2.0
termcolor==1.1.0
wrapt==1.12.1
tensorboard==2.2.0
tensorflow-cpu==2.2.0
tensorflow-probability==0.10.1
pyspark==2.2.2
pyspark_dist_explore==0.1.8
cytoolz==0.11.0
-e "git+https://${GITHUB_OAUTH_TOKEN}@github.com/tigerrepository/TigerML.git@master#egg=tigerml&subdirectory=python"
-e "git+https://${GITHUB_OAUTH_TOKEN}@github.com/tigerrepository/Intelligent_binning.git@master#egg=Intelligent_binning"
-e "git+https://${GITHUB_OAUTH_TOKEN}@github.com/tigerrepository/BayesianLearning.git@master#egg=BayesFramework"
