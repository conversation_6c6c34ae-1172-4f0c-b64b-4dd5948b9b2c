channels:
  - conda-forge
dependencies:
- deap==1.3.1 # tigerml-automl
- tpot==0.11.7 # tigerml-automl
- pip:
  - joblib==1.1.0
  - tweepy==4.10.0 # Data Extraction
  - searchtweets==1.7.6 # Data Extraction
  - pytrends==4.8.0 # Data Extraction
  - dtw-python==1.3.0 #Emerging Trends
  - tslearn==0.5.2 #Emerging Trends
  - bertopic==0.11.0 # All others packages are for tigernlp
  - contractions==0.1.72
  - datasketch==1.5.8
  - dotmap==1.3.30
  - emoji==2.0.0
  - fuzzywuzzy==0.18.0
  - gensim==4.2.0
  - hdbscan==0.8.28
  - keybert==0.5.1
  - kneed==0.7.0
  - networkx==2.8.6
  - nltk==3.7
  - plotly==5.9.0
  - punctuator==0.9.6
  - pyLDAvis==3.3.1
  - pyspellchecker==0.7.0
  - scikit-multilearn==0.2.0
  - sentence_transformers==2.2.2 
  - spacy==3.3.0
  - tensorflow==2.9.1
  - tensorflow_hub==0.12.0
  - transformers==4.18.0
  - umap==0.1.1
  - umap_learn==0.5.3
  - wordcloud==*******
  - mxtheme==0.3.11 # tigerml-automl
  - holidays==0.17 # tigerml-automl
  - fabric==2.5.0  # tigerml-automl
  - patchwork==1.0.1 # tigerml-automl
  - gluonts==0.11.3 # tigerml-automl
  - prophet==1.1.1  # tigerml-automl